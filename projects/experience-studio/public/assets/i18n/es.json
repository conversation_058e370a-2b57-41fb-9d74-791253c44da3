{"common": {"buttons": {"submit": "Enviar", "cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "back": "Atrás", "next": "Siguient<PERSON>", "previous": "Anterior", "retry": "Reintentar", "refresh": "Actualizar", "export": "Exportar", "import": "Importar", "upload": "Subir", "download": "<PERSON><PERSON><PERSON>", "copy": "Copiar", "paste": "<PERSON><PERSON><PERSON>", "cut": "Cortar", "undo": "<PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON>", "search": "Buscar", "filter": "Filtrar", "sort": "Ordenar", "clear": "Limpiar", "reset": "Restablecer", "apply": "Aplicar", "confirm": "Confirmar", "ok": "Aceptar", "yes": "Sí", "no": "No", "loading": "Cargando...", "tryExperienceStudio": "Probar Experience Studio", "getStarted": "Comenzar", "signOut": "<PERSON><PERSON><PERSON>", "retryGeneration": "Reintentar Generación"}, "labels": {"name": "Nombre", "email": "Correo Electrónico", "password": "Contraseña", "username": "Usuario", "description": "Descripción", "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "Tipo", "status": "Estado", "date": "<PERSON><PERSON>", "time": "<PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "version": "Versión", "language": "Idioma", "theme": "<PERSON><PERSON>", "settings": "Configuración", "preferences": "Preferencias", "profile": "Perfil", "account": "C<PERSON><PERSON>", "project": "Proyecto", "file": "Archivo", "folder": "Carpeta", "image": "Imagen", "document": "Documento"}, "messages": {"loading": "Cargando...", "saving": "Guardando...", "saved": "Guardado exitosamente", "error": "Ocurrió un error", "success": "Operación completada exitosamente", "warning": "Advertencia", "info": "Información", "noData": "No hay datos disponibles", "noResults": "No se encontraron resultados", "emptyState": "Nada que mostrar aquí", "comingSoon": "Próximamente", "underConstruction": "En Construcción", "maintenance": "Sistema en mantenimiento"}, "validation": {"required": "Este campo es obligatorio", "email": "Por favor ingrese un correo electrónico válido", "minLength": "Se requieren mínimo {{min}} caracteres", "maxLength": "Máximo {{max}} caracteres permitidos", "pattern": "Formato inválido", "numeric": "Solo se permiten númer<PERSON>", "alphanumeric": "Solo se permiten letras y números"}, "accessibility": {"closeModal": "Cerrar modal", "openMenu": "<PERSON><PERSON><PERSON>", "closeMenu": "<PERSON><PERSON><PERSON>", "toggleTheme": "Cambiar tema", "navigateHome": "Navegar a la página de inicio", "currentLanguage": "Idioma actual: {{language}}", "switchToLanguage": "Cambiar a {{language}}", "languageSelector": "Selector de idioma", "loading": "Cargando contenido", "error": "Ocurrió un error", "success": "Éxito"}}, "landing": {"hero": {"title": "¡Sueña, <PERSON>struye, Lanza!", "description": "Experience Studio combina arte y ciencia para dar vida a tus ideas", "subheading": "¿Qué te gustaría construir hoy?"}, "cards": {"generateWireframes": {"title": "Generar Wireframes", "description": "Genera wireframes limpios y estructurados a partir de tus ideas en segundos para una planificación de diseño más rápida."}, "imageToApplication": {"title": "Imagen a Aplicación", "description": "Convierte imágenes de UI o bocetos en código de aplicación funcional al instante sin esfuerzo manual."}, "promptToApplication": {"title": "Prompt a Aplicación", "description": "Convierte prompts en lenguaje natural en aplicaciones listas para usar con facilidad y velocidad."}, "designAccessibility": {"title": "Accesibilidad de Diseño", "description": "Asegúrate de que tus diseños sean accesibles e inclusivos para todos en todas las plataformas."}}, "healthCheck": {"servicesDown": "Los servicios de la aplicación están inactivos. Por favor contacta al administrador para reiniciar el servicio", "checkingServices": "Verificando servicios...", "serviceUnavailable": "Los servicios no están disponibles. Por favor contacta al administrador para reiniciar los servicios de la aplicación"}, "developerDemo": {"title": "🧪 Demos de Desarrollador", "description": "Vista previa de próximas funciones e integraciones de API", "wireframeArtifacts": {"title": "Demo de Artefactos de Wireframe", "description": "Vista previa de cómo se mostrarán los tokens del sistema de diseño en la pestaña de artefactos"}}}, "navigation": {"home": "<PERSON><PERSON>o", "projects": "Proyectos", "settings": "Configuración", "help": "<PERSON><PERSON><PERSON>", "about": "Acerca de", "contact": "Contacto", "documentation": "Documentación", "support": "Soporte", "feedback": "Comentarios", "logout": "<PERSON><PERSON><PERSON>", "profile": "Perfil", "account": "C<PERSON><PERSON>", "preferences": "Preferencias"}, "tabs": {"preview": "Vista Previa", "code": "Código", "artifacts": "Artefactos", "overview": "Resumen", "history": "Historial", "logs": "Registros", "settings": "Configuración"}, "codeWindow": {"tabs": {"preview": "Vista Previa", "code": "Código", "artifacts": "Artefactos", "overview": "Resumen", "history": "Historial"}, "tooltips": {"previewDisabled": "La vista previa estará disponible una vez que la aplicación sea desplegada", "codeDisabled": "El código estará disponible una vez generado", "artifactsDisabled": "Ver artefactos generados y tokens de diseño", "overviewDisabled": "Ver resumen del diseño UI", "historyDisabled": "Ver historial de conversación", "navigateHome": "Navegar a la página de inicio", "togglePanel": "Alternar panel izquierdo", "fullscreen": "Alternar pantalla completa", "export": "Exportar proyecto como Zip"}, "projectPreview": {"title": "Vista Previa del Proyecto", "loadingData": "Cargando datos del proyecto...", "untitledProject": "Proyecto Sin Título", "noDescription": "No hay descripción disponible", "metadata": {"type": "Tipo:", "lastModified": "Última Modificación:", "created": "Creado:", "status": "Estado:"}, "backToProjects": "Volver a Proyectos"}}, "chatWindow": {"placeholders": {"default": "Escribe tu mensaje aquí...", "imageUpload": "Describe lo que quieres construir o sube una imagen...", "promptToApp": "Describe la aplicación que quieres construir...", "wireframe": "Describe el wireframe que quieres generar..."}, "stepper": {"generating": "Generando", "building": "Construyendo", "deploying": "Desplegando", "completed": "Completado", "failed": "<PERSON><PERSON>"}}, "promptBar": {"placeholders": {"default": "Escribe tu prompt aquí...", "imageToApp": "Describe lo que quieres construir basado en la imagen subida...", "promptToApp": "Describe la aplicación que quieres construir..."}, "validation": {"empty": "¡La entrada no puede estar vacía!", "tooLong": "¡La entrada excede la longitud máxima permitida!", "fileSize": "El tamaño del archivo debe ser menor a 5MB", "fileType": "Solo se permiten archivos de imagen (JPEG, PNG, GIF, WEBP, SVG)", "folderUpload": "No se pueden subir carpetas", "maxFiles": "Solo se puede subir {{max}} imagen a la vez"}, "fileUpload": {"attachImage": "Adjuntar Imagen de Referencia", "attachDocuments": "Adjuntar Documentos", "uploadFromComputer": "Subir (.txt) Desde Computadora", "imageDocsFromComputer": "<PERSON>n, docs desde Computadora", "removeFile": "Eliminar {{fileName}}"}}, "recentProjects": {"title": "Proyectos Recientes", "categories": {"recent": "Recientes", "all": "Todos"}, "emptyState": {"noProjects": "No se Encontraron Proyectos", "recentEmpty": "No has creado proyectos recientemente. ¡Comienza a construir algo increíble!", "allEmpty": "No hay proyectos disponibles en este momento. Crea tu primer proyecto para comenzar."}, "actions": {"open": "Abrir", "view": "<PERSON>er", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "duplicate": "Duplicar"}, "loading": {"checkingServices": "Verificando servicios...", "loadingProjects": "Cargando proyectos..."}, "errors": {"fetchFailed": "No se pudieron obtener tus proyectos. Por favor intenta de nuevo más tarde."}}, "generationAccordion": {"version": "Versión {{number}}", "fileCount": "{{count}} archivos", "timestamps": {"justNow": "<PERSON><PERSON> mismo", "minutesAgo": "hace {{minutes}} minutos", "hoursAgo": "hace {{hours}} horas", "daysAgo": "hace {{days}} días"}, "error": {"title": "Detalles del Error:", "suggestion": "Intenta generar de nuevo o verifica tu solicitud por cualquier problema.", "retrying": "Reintentando..."}}, "toast": {"messages": {"startingGeneration": "Iniciando generación de {{type}}", "featureInBuild": "Esta función está en modo de construcción.", "waitingForServices": "Por favor espera mientras verificamos la disponibilidad del servicio...", "contactAdmin": "Por favor contacta al administrador para reiniciar los servicios de la aplicación", "applicationReady": "Aplicación lista", "changesSaved": "Cambios guardados exitosamente", "savingChanges": "Guardando tus cambios...", "loadingResources": "Cargando recursos de la aplicación..."}}, "login": {"title": "Experience Studio", "description": "Experience Studio acelera todo tu ciclo de vida de diseño y desarrollo con agentes de IA. Construye interfaces, prototipos y experiencias digitales, todo en un solo lugar.", "loading": "Cargando tu experiencia...", "animatedText": {"generate": "Generar", "wireframe": "Wireframe", "application": "Aplicación"}, "error": "Error al cargar. Por favor intenta de nuevo."}, "profile": {"modal": {"title": "Perfil", "close": "Cerrar modal de perfil", "signOut": "<PERSON><PERSON><PERSON> sesi<PERSON> de tu cuenta"}}, "errors": {"page": {"letters": {"e": "Detención de Ejecución", "r1": "Problema de Solicitud", "r2": "Resultado: <PERSON><PERSON>", "o": "Operación Bloqueada", "r3": "Reintentar/Reportar"}}, "general": {"somethingWentWrong": "Algo salió mal", "tryAgain": "Por favor intenta de nuevo", "contactSupport": "Contacta soporte si el problema persiste"}}, "theme": {"light": "<PERSON><PERSON><PERSON>", "dark": "Oscuro", "auto": "Automático", "toggleTheme": "Cambiar tema"}}