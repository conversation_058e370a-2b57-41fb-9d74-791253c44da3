{"common": {"buttons": {"submit": "Submit", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "retry": "Retry", "refresh": "Refresh", "export": "Export", "import": "Import", "upload": "Upload", "download": "Download", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "search": "Search", "filter": "Filter", "sort": "Sort", "clear": "Clear", "reset": "Reset", "apply": "Apply", "confirm": "Confirm", "ok": "OK", "yes": "Yes", "no": "No", "loading": "Loading...", "tryExperienceStudio": "Try Experience Studio", "getStarted": "Get Started", "signOut": "Sign Out", "retryGeneration": "Retry Generation"}, "labels": {"name": "Name", "email": "Email", "password": "Password", "username": "Username", "description": "Description", "title": "Title", "type": "Type", "status": "Status", "date": "Date", "time": "Time", "size": "Size", "version": "Version", "language": "Language", "theme": "Theme", "settings": "Settings", "preferences": "Preferences", "profile": "Profile", "account": "Account", "project": "Project", "file": "File", "folder": "Folder", "image": "Image", "document": "Document"}, "messages": {"loading": "Loading...", "saving": "Saving...", "saved": "Saved successfully", "error": "An error occurred", "success": "Operation completed successfully", "warning": "Warning", "info": "Information", "noData": "No data available", "noResults": "No results found", "emptyState": "Nothing to show here", "comingSoon": "Coming Soon", "underConstruction": "Under Construction", "maintenance": "System under maintenance"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Minimum {{min}} characters required", "maxLength": "Maximum {{max}} characters allowed", "pattern": "Invalid format", "numeric": "Only numbers allowed", "alphanumeric": "Only letters and numbers allowed"}, "accessibility": {"closeModal": "Close modal", "openMenu": "Open menu", "closeMenu": "Close menu", "toggleTheme": "Toggle theme", "navigateHome": "Navigate to home page", "currentLanguage": "Current language: {{language}}", "switchToLanguage": "Switch to {{language}}", "languageSelector": "Language selector", "loading": "Loading content", "error": "Error occurred", "success": "Success"}}, "landing": {"hero": {"title": "Dream Build Launch!", "description": "Experience Studio blends art and science to bring your ideas to life", "subheading": "What would you like to build today?"}, "cards": {"generateWireframes": {"title": "Generate Wireframes", "description": "Generate clean, structured wireframes from your ideas in seconds for faster design planning."}, "imageToApplication": {"title": "Image to Application", "description": "Convert UI images or sketches into working application code instantly without manual effort."}, "promptToApplication": {"title": "Prompt to Application", "description": "Turn natural language prompts into ready-to-use applications with ease and speed."}, "designAccessibility": {"title": "Design Accessibility", "description": "Ensure your designs are accessible and inclusive for everyone across all platforms."}}, "healthCheck": {"servicesDown": "App Services are down. Please contact admin to restart the service", "checkingServices": "Checking services...", "serviceUnavailable": "Services are not available. Please contact admin to restart the app services"}, "developerDemo": {"title": "🧪 Developer Demos", "description": "Preview upcoming features and API integrations", "wireframeArtifacts": {"title": "Wireframe Artifacts Demo", "description": "Preview how design system tokens will be displayed in the artifacts tab"}}}, "navigation": {"home": "Home", "projects": "Projects", "settings": "Settings", "help": "Help", "about": "About", "contact": "Contact", "documentation": "Documentation", "support": "Support", "feedback": "<PERSON><PERSON><PERSON>", "logout": "Logout", "profile": "Profile", "account": "Account", "preferences": "Preferences"}, "tabs": {"preview": "Preview", "code": "Code", "artifacts": "Artifacts", "overview": "Overview", "history": "History", "logs": "Logs", "settings": "Settings"}, "codeWindow": {"tabs": {"preview": "Preview", "code": "Code", "artifacts": "Artifacts", "overview": "Overview", "history": "History"}, "tooltips": {"previewDisabled": "Preview will be available once the application is deployed", "codeDisabled": "Code will be available once generated", "artifactsDisabled": "View generated artifacts and design tokens", "overviewDisabled": "View UI design overview", "historyDisabled": "View conversation history", "navigateHome": "Navigate to home page", "togglePanel": "Toggle left panel", "fullscreen": "Toggle fullscreen", "export": "Export project as Zip"}, "projectPreview": {"title": "Project Preview", "loadingData": "Loading project data...", "untitledProject": "Untitled Project", "noDescription": "No description available", "metadata": {"type": "Type:", "lastModified": "Last Modified:", "created": "Created:", "status": "Status:"}, "backToProjects": "Back to Projects"}}, "chatWindow": {"placeholders": {"default": "Type your message here...", "imageUpload": "Describe what you want to build or upload an image...", "promptToApp": "Describe the application you want to build...", "wireframe": "Describe the wireframe you want to generate..."}, "stepper": {"generating": "Generating", "building": "Building", "deploying": "Deploying", "completed": "Completed", "failed": "Failed"}}, "promptBar": {"placeholders": {"default": "Type your prompt here...", "imageToApp": "Describe what you want to build based on the uploaded image...", "promptToApp": "Describe the application you want to build..."}, "validation": {"empty": "Input cannot be empty!", "tooLong": "Input exceeds the maximum allowed length!", "fileSize": "File size must be less than 5MB", "fileType": "Only image files (JPEG, PNG, GIF, WEBP, SVG) are allowed", "folderUpload": "Folders cannot be uploaded", "maxFiles": "Only {{max}} image can be uploaded at a time"}, "fileUpload": {"attachImage": "Attach Ref Img", "attachDocuments": "Attach Documents", "uploadFromComputer": "Upload (.txt) From Computer", "imageDocsFromComputer": "<PERSON>, docs from Computer", "removeFile": "Remove {{fileName}}"}}, "recentProjects": {"title": "Recent Projects", "categories": {"recent": "Recent", "all": "All"}, "emptyState": {"noProjects": "No Projects Found", "recentEmpty": "You haven't created any projects recently. Start building something amazing!", "allEmpty": "No projects available at the moment. Create your first project to get started."}, "actions": {"open": "Open", "view": "View", "edit": "Edit", "delete": "Delete", "duplicate": "Duplicate"}, "loading": {"checkingServices": "Checking services...", "loadingProjects": "Loading projects..."}, "errors": {"fetchFailed": "Unable to fetch your projects. Please try again later."}}, "generationAccordion": {"version": "Version {{number}}", "fileCount": "{{count}} files", "timestamps": {"justNow": "Just now", "minutesAgo": "{{minutes}} minutes ago", "hoursAgo": "{{hours}} hours ago", "daysAgo": "{{days}} days ago"}, "error": {"title": "Error Details:", "suggestion": "Try generating again or check your request for any issues.", "retrying": "Retrying..."}}, "toast": {"messages": {"startingGeneration": "Starting {{type}} generation", "featureInBuild": "This feature is in build mode.", "waitingForServices": "Please wait while we check service availability...", "contactAdmin": "Please contact admin to restart the app services", "applicationReady": "Application ready", "changesSaved": "Changes saved successfully", "savingChanges": "Saving your changes...", "loadingResources": "Loading application resources..."}}, "login": {"title": "Experience Studio", "description": "Experience Studio accelerates your entire design and development lifecycle with AI agents. Build interfaces, prototypes, and digital experiences, all in one place.", "loading": "Loading your experience...", "animatedText": {"generate": "Generate", "wireframe": "Wireframe", "application": "Application"}, "error": "Failed to load. Please try again."}, "profile": {"modal": {"title": "Profile", "close": "Close profile modal", "signOut": "Sign out of your account"}}, "errors": {"page": {"letters": {"e": "Execution Halt", "r1": "Request Issue", "r2": "Result: Failure", "o": "Operation Blocked", "r3": "Retry/Report"}}, "general": {"somethingWentWrong": "Something went wrong", "tryAgain": "Please try again", "contactSupport": "Contact support if the problem persists"}}, "fileUpload": {"dragDrop": "Drag and drop files here or click to browse", "selectFiles": "Select Files", "supportedFormats": "Supported formats: {{formats}}", "maxSize": "Maximum file size: {{size}}", "uploading": "Uploading...", "uploadComplete": "Upload complete", "uploadFailed": "Upload failed"}, "search": {"placeholder": "Search...", "noResults": "No results found", "searching": "Searching...", "clearSearch": "Clear search", "searchResults": "Search results"}, "pagination": {"previous": "Previous", "next": "Next", "page": "Page", "of": "of", "showing": "Showing {{start}} to {{end}} of {{total}} results", "itemsPerPage": "Items per page"}, "filters": {"all": "All", "active": "Active", "inactive": "Inactive", "recent": "Recent", "popular": "Popular", "featured": "Featured", "clearFilters": "Clear filters", "applyFilters": "Apply filters"}, "keyboardShortcuts": {"title": "Keyboard Shortcuts", "general": "General", "navigation": "Navigation", "editing": "Editing", "shortcuts": {"save": "Save", "undo": "Undo", "redo": "Redo", "copy": "Copy", "paste": "Paste", "cut": "Cut", "selectAll": "Select All", "find": "Find", "newProject": "New Project", "openProject": "Open Project", "closeProject": "Close Project"}}, "theme": {"light": "Light", "dark": "Dark", "auto": "Auto", "toggleTheme": "Toggle theme"}, "constants": {"animatedTexts": {"imageToApp": ["an application based on this image..", "a web app that looks like this image..", "a UI similar to this design..", "a responsive interface based on this mockup..", "a functional app from this wireframe..", "code that implements this design..", "a working prototype of this UI..", "an interactive version of this mockup..", "a component that matches this design..", "a pixel-perfect implementation of this image.."], "uiDesign": ["a modern landing page for..", "a mobile app interface for..", "a dashboard design with..", "a user profile page for..", "a product showcase page for..", "a contact form design for..", "a navigation menu for..", "a card-based layout for..", "a responsive design for..", "a minimalist interface for.."], "promptToApp": ["a web app that..", "an internal tool that..", "an e-commerce site with..", "a portfolio website for my..", "a booking system for..", "a social media platform for..", "a customer support portal with..", "a real-time analytics dashboard for..", "a landing page for my..", "a dashboard to.."]}, "techOptions": {"react": "React", "angular": "<PERSON><PERSON> (Coming Soon)", "vue": "Vue (Coming Soon)"}, "prefilledPrompts": {"promptToApp": "Create a responsive web app by carefully analyzing and implementing the features and requirements detailed in the uploaded document."}}}