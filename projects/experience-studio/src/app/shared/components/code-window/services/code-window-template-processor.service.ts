import { Injectable, signal, computed, inject, DestroyRef, NgZone } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Observable } from 'rxjs';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { CodeWindowFileManagerService, FileModel } from './code-window-file-manager.service';

export interface TemplateFile {
  name: string;
  content: string;
  path?: string;
  type?: string;
}

export interface ProcessingResult {
  validFiles: FileModel[];
  invalidFiles: Array<{ file: TemplateFile; reason: string }>;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowTemplateProcessorService {
  private destroyRef = inject(DestroyRef);
  private ngZone = inject(NgZone);
  private sanitizer = inject(DomSanitizer);
  private fileManagerService = inject(CodeWindowFileManagerService);

  readonly isProcessingTemplate = signal<boolean>(false);
  readonly templateFileCount = signal<number>(0);
  readonly lastProcessedTemplate = signal<string>('');
  readonly processingErrors = signal<string[]>([]);

  private templateProcessingStatus$ = new BehaviorSubject<'idle' | 'processing' | 'completed' | 'failed'>('idle');
  private templateValidationResults$ = new BehaviorSubject<ProcessingResult | null>(null);

  readonly templateProcessingStatus = this.templateProcessingStatus$.asObservable();
  readonly templateValidationResults = this.templateValidationResults$.asObservable();

  readonly canProcessTemplate = computed(() => !this.isProcessingTemplate());
  readonly hasProcessingErrors = computed(() => this.processingErrors().length > 0);
  readonly templateProcessingHealth = computed(() => {
    if (this.isProcessingTemplate()) return 'processing';
    if (this.hasProcessingErrors()) return 'error';
    if (this.templateFileCount() > 0) return 'completed';
    return 'idle';
  });

  constructor() {
    this.initializeTemplateProcessor();
  }

  subscribeToTemplateLoadingService(templateFiles$: Observable<TemplateFile[]>): Observable<ProcessingResult> {
    return new Observable(subscriber => {
      templateFiles$.pipe(
        takeUntilDestroyed(this.destroyRef)
      ).subscribe({
        next: (templateFiles) => {

          try {
            if (templateFiles && templateFiles.length > 0) {
              this.startTemplateProcessing();

              const result = this.processTemplateFiles(templateFiles);

              if (result.validFiles.length > 0) {
                this.completeTemplateProcessing(result);
                subscriber.next(result);
              } else {
                this.failTemplateProcessing('No valid template files to process');
                subscriber.error(new Error('No valid template files'));
              }
            } else {

              this.resetTemplateProcessing();
              subscriber.next({ validFiles: [], invalidFiles: [] });
            }
          } catch (error) {

            this.failTemplateProcessing(error instanceof Error ? error.message : String(error));
            subscriber.error(error);
          }
        },
        error: (error) => {

          this.failTemplateProcessing('Template loading service error');
          subscriber.error(error);
        }
      });
    });
  }

  processTemplateFiles(templateFiles: TemplateFile[]): ProcessingResult {
    const validFiles: FileModel[] = [];
    const invalidFiles: Array<{ file: TemplateFile; reason: string }> = [];

    templateFiles.forEach(templateFile => {
      try {
        const validation = this.validateTemplateFile(templateFile);

        if (validation.isValid) {
          const fileModel = this.convertTemplateToFileModel(templateFile);
          validFiles.push(fileModel);
        } else {
          invalidFiles.push({
            file: templateFile,
            reason: validation.reason || 'Unknown validation error'
          });
        }
      } catch (error) {
        invalidFiles.push({
          file: templateFile,
          reason: error instanceof Error ? error.message : 'Processing error'
        });
      }
    });

    return { validFiles, invalidFiles };
  }

  validateTemplateFile(templateFile: TemplateFile): { isValid: boolean; reason?: string } {
    if (!templateFile) {
      return { isValid: false, reason: 'Template file is null or undefined' };
    }

    if (!templateFile.name || typeof templateFile.name !== 'string') {
      return { isValid: false, reason: 'Template file name is missing or invalid' };
    }

    if (!templateFile.content || typeof templateFile.content !== 'string') {
      return { isValid: false, reason: 'Template file content is missing or invalid' };
    }

    if (templateFile.content.trim().length < 10) {
      return { isValid: false, reason: 'Template file content is too short' };
    }

    const validExtensions = ['.js', '.jsx', '.ts', '.tsx', '.html', '.css', '.scss', '.json', '.md', '.txt'];
    const hasValidExtension = validExtensions.some(ext => templateFile.name.toLowerCase().endsWith(ext));

    if (!hasValidExtension) {
      return { isValid: false, reason: 'Template file has unsupported extension' };
    }

    return { isValid: true };
  }

  convertTemplateToFileModel(templateFile: TemplateFile): FileModel {
    const fileName = templateFile.name;
    const language = this.fileManagerService.getLanguageFromPath(fileName);

    return {
      name: fileName,
      fileName: fileName,
      language: language,
      content: templateFile.content,
      path: templateFile.path || fileName,
      type: (templateFile.type === 'folder' ? 'folder' : 'file') as 'file' | 'folder',
    };
  }

  enhanceHTMLWithCSS(content: string): string {
    if (!content || typeof content !== 'string') {
      return content;
    }

    if (content.includes('<style>') || content.includes('stylesheet')) {
      return content;
    }

    const cssEnhancement = `
<style>
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
  }
  .container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }
</style>`;

    if (content.includes('</head>')) {
      return content.replace('</head>', `${cssEnhancement}\n</head>`);
    } else if (content.includes('<html>')) {
      return content.replace('<html>', `<html>\n<head>${cssEnhancement}\n</head>`);
    } else {
      return `<head>${cssEnhancement}</head>\n${content}`;
    }
  }

  startTemplateProcessing(): void {
    this.ngZone.run(() => {
      this.isProcessingTemplate.set(true);
      this.templateProcessingStatus$.next('processing');
      this.processingErrors.set([]);
    });
  }

  completeTemplateProcessing(result: ProcessingResult): void {
    this.ngZone.run(() => {
      this.isProcessingTemplate.set(false);
      this.templateProcessingStatus$.next('completed');
      this.templateFileCount.set(result.validFiles.length);
      this.templateValidationResults$.next(result);

      if (result.invalidFiles.length > 0) {
        const errors = result.invalidFiles.map(f => f.reason);
        this.processingErrors.set(errors);
      }
    });
  }

  failTemplateProcessing(error: string): void {
    this.ngZone.run(() => {
      this.isProcessingTemplate.set(false);
      this.templateProcessingStatus$.next('failed');
      this.processingErrors.set([error]);
    });
  }

  resetTemplateProcessing(): void {
    this.ngZone.run(() => {
      this.isProcessingTemplate.set(false);
      this.templateProcessingStatus$.next('idle');
      this.templateFileCount.set(0);
      this.lastProcessedTemplate.set('');
      this.processingErrors.set([]);
      this.templateValidationResults$.next(null);
    });
  }

  private initializeTemplateProcessor(): void {
    this.destroyRef.onDestroy(() => {
      this.resetTemplateProcessing();
    });
  }

  getCurrentTemplateState(): {
    signals: {
      isProcessing: boolean;
      fileCount: number;
      hasErrors: boolean;
      canProcess: boolean;
    };
    status: string;
    health: string;
    errors: string[];
  } {
    return {
      signals: {
        isProcessing: this.isProcessingTemplate(),
        fileCount: this.templateFileCount(),
        hasErrors: this.hasProcessingErrors(),
        canProcess: this.canProcessTemplate(),
      },
      status: this.templateProcessingStatus$.value,
      health: this.templateProcessingHealth(),
      errors: this.processingErrors()
    };
  }
}
