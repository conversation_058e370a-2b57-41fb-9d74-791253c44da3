import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class CodeWindowUtilityService {

  generateNodeId(): string {
    return `ui-design-node-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  generateRegenerationSessionId(): string {
    const counter = Date.now();
    return `regen-session-${Date.now()}-${counter}-${Math.random().toString(36).substring(2, 11)}`;
  }

  trackByLineNumber(_index: number, lineNumber: number): number {
    return lineNumber;
  }

  isValidHexColor(color: string): boolean {
    const hexColorRegex = /^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return hexColorRegex.test(color);
  }

  extractTimestamp(log: string): string {
    const parts = log.split(' - ');
    if (parts.length >= 1) {
      const timestamp = parts[0];

      if (timestamp.match(/^\d{2}:\d{2}:\d{2}/)) {
        const now = new Date();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        return `${month}/${day} ${timestamp}`;
      }
    }

    const now = new Date();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const time = now.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
    return `${month}/${day} ${time}`;
  }

  extractLogContent(log: string): string {
    const parts = log.split(' - ');
    if (parts.length >= 3) {
      return parts.slice(2).join(' - ');
    }
    return log;
  }

  createLogHash(log: string): string {
    const timestamp = this.extractTimestamp(log);
    const content = this.extractLogContent(log);
    const length = log.length;

    return `${timestamp}-${content.substring(0, 50)}-${length}`;
  }

  getLanguageFromPath(path: string): string {
    const extension = path.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'ts':
        return 'typescript';
      case 'js':
        return 'javascript';
      case 'html':
        return 'html';
      case 'css':
        return 'css';
      case 'scss':
        return 'scss';
      case 'json':
        return 'json';
      case 'md':
        return 'markdown';
      case 'tsx':
        return 'typescript';
      case 'jsx':
        return 'javascript';
      case 'vue':
        return 'vue';
      case 'py':
        return 'python';
      case 'java':
        return 'java';
      case 'php':
        return 'php';
      case 'rb':
        return 'ruby';
      case 'go':
        return 'go';
      case 'rs':
        return 'rust';
      case 'cpp':
      case 'cc':
      case 'cxx':
        return 'cpp';
      case 'c':
        return 'c';
      case 'cs':
        return 'csharp';
      case 'xml':
        return 'xml';
      case 'yaml':
      case 'yml':
        return 'yaml';
      case 'sql':
        return 'sql';
      case 'sh':
        return 'shell';
      case 'dockerfile':
        return 'dockerfile';
      default:
        return 'plaintext';
    }
  }

  isIntroMessage(message: any): boolean {
    if (!message || !message.text) {
      return false;
    }

    const text = message.text.toLowerCase();

    const introPatterns = [
      'preparing your code',
      'getting ready to',
      'setting up code',
      'initializing code',
      'analyzing your request',
      'understanding your changes',
      'reviewing your code',
      'processing your edit request',
      'examining the codebase',
      'evaluating your modifications'
    ];

    const isIntro = introPatterns.some(pattern => text.includes(pattern));

    const completionPatterns = [
      'edit completed',
      'regeneration completed',
      'successfully updated',
      'processing complete'
    ];

    const isCompletion = completionPatterns.some(pattern => text.includes(pattern));

    return isIntro && !isCompletion;
  }

  generateUniqueId(): string {
    return `id-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  formatTimestamp(timestamp?: number): string {
    const date = timestamp ? new Date(timestamp) : new Date();
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  cleanUrl(url: string): string {
    if (!url || typeof url !== 'string') {
      return '';
    }

    try {
      let cleanedUrl = url.trim();
      const urlObj = new URL(cleanedUrl);

      urlObj.searchParams.delete('_');
      urlObj.searchParams.delete('timestamp');

      return urlObj.toString();
    } catch (error) {
      return url.trim();
    }
  }

  debounceFunction<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: number;

    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = window.setTimeout(() => func.apply(this, args), delay);
    };
  }

  throttleFunction<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;

    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
}
