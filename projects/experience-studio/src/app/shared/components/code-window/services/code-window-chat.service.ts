import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface ChatMessage {
  id: string;
  text: string;
  type: 'user' | 'ai' | 'system';
  timestamp: number;
  isTyping?: boolean;
  isIntroMessage?: boolean;
  targetMessageId?: string;
  originalText?: string;
}

export interface IntroMessageState {
  targetMessageId: string;
  text: string;
  isActive: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowChatService {

  private lightMessages$ = new BehaviorSubject<ChatMessage[]>([]);
  private currentActiveMessageId: string | null = null;
  private typewriterTimeouts: { [key: string]: number } = {};
  private introMessageStates = new Map<string, IntroMessageState>();

  private activeRegenerationSessions = new Map<string, any>();
  private regenerationSessionCounter = 0;

  constructor() {}

  get lightMessages(): Observable<ChatMessage[]> {
    return this.lightMessages$.asObservable();
  }

  get lightMessagesValue(): ChatMessage[] {
    return this.lightMessages$.value;
  }

  get lightMessagesArray(): ChatMessage[] {
    return this.lightMessages$.value;
  }

  set lightMessagesArray(messages: ChatMessage[]) {
    this.lightMessages$.next(messages);
  }

  addMessage(message: ChatMessage): void {
    const currentMessages = this.lightMessages$.value;
    this.lightMessages$.next([...currentMessages, message]);
  }

  updateMessage(messageId: string, updates: Partial<ChatMessage>): void {
    const currentMessages = this.lightMessages$.value;
    const updatedMessages = currentMessages.map(msg => 
      msg.id === messageId ? { ...msg, ...updates } : msg
    );
    this.lightMessages$.next(updatedMessages);
  }

  removeMessage(messageId: string): void {
    const currentMessages = this.lightMessages$.value;
    const filteredMessages = currentMessages.filter(msg => msg.id !== messageId);
    this.lightMessages$.next(filteredMessages);
  }

  getLastMessage(): ChatMessage | null {
    const messages = this.lightMessages$.value;
    return messages.length > 0 ? messages[messages.length - 1] : null;
  }

  findMessageById(messageId: string): ChatMessage | null {
    return this.lightMessages$.value.find(msg => msg.id === messageId) || null;
  }

  filterMessages(predicate: (message: ChatMessage) => boolean): ChatMessage[] {
    return this.lightMessages$.value.filter(predicate);
  }

  removeTemporaryLoadingMessages(): void {
    const filteredMessages = this.lightMessages$.value.filter(msg => {
      const isTemporaryLoadingMessage = 
        msg.text?.includes('Analyzing your design') ||
        msg.text?.includes('Getting your files ready') ||
        msg.text?.includes('Processing your request') ||
        msg.text?.includes('Generating your application') ||
        (msg.isTyping && msg.type === 'ai');
      
      return !isTemporaryLoadingMessage;
    });
    
    this.lightMessages$.next(filteredMessages);
  }

  cleanupLegacyEditMessages(): void {
    const filteredMessages = this.lightMessages$.value.filter(msg => {
      const isLegacyEditMessage = 
        msg.text?.includes('Editing') && msg.text?.includes('...') ||
        msg.text?.includes('Processing edit request') ||
        msg.text?.includes('Applying changes');
      
      return !isLegacyEditMessage;
    });
    
    this.lightMessages$.next(filteredMessages);
  }

  isIntroMessage(message: ChatMessage): boolean {
    if (!message || !message.text) {
      return false;
    }

    const introPatterns = [
      'I\'ll help you',
      'Let me help you',
      'I\'m going to',
      'I\'ll start by',
      'Let me start by',
      'I\'ll begin by',
      'Let me begin by'
    ];

    return introPatterns.some(pattern => 
      message.text.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  handleChatMessageTextReplacement(state: IntroMessageState): void {
    if (!state.targetMessageId || !state.text) {
      return;
    }

    const targetMessage = this.findMessageById(state.targetMessageId);
    if (!targetMessage) {
      return;
    }

    if (!targetMessage.originalText) {
      this.updateMessage(state.targetMessageId, {
        originalText: targetMessage.text
      });
    }

    this.introMessageStates.set(state.targetMessageId, state);
    this.currentActiveMessageId = state.targetMessageId;

    this.startTypewriterEffectForMessage(state.text, state.targetMessageId);
  }

  restoreOriginalChatMessageText(): void {
    if (!this.currentActiveMessageId) {
      return;
    }

    const targetMessage = this.findMessageById(this.currentActiveMessageId);
    if (targetMessage && targetMessage.originalText) {
      this.updateMessage(this.currentActiveMessageId, {
        text: targetMessage.originalText,
        isTyping: false
      });
    }

    this.introMessageStates.delete(this.currentActiveMessageId);
    this.currentActiveMessageId = null;
  }

  startTypewriterEffectForMessage(fullText: string, messageId: string): void {
    const typingSpeed = 30;
    let charIndex = 0;

    if (this.typewriterTimeouts[messageId]) {
      clearTimeout(this.typewriterTimeouts[messageId]);
    }

    const typeNextChar = () => {
      if (charIndex < fullText.length) {
        const currentText = fullText.substring(0, charIndex + 1);
        this.updateMessage(messageId, {
          text: currentText,
          isTyping: true
        });
        
        charIndex++;
        this.typewriterTimeouts[messageId] = window.setTimeout(typeNextChar, typingSpeed);
      } else {
        this.updateMessage(messageId, {
          text: fullText,
          isTyping: false
        });
        delete this.typewriterTimeouts[messageId];
      }
    };

    typeNextChar();
  }

  clearAllTypewriterTimeouts(): void {
    Object.values(this.typewriterTimeouts).forEach(timeout => clearTimeout(timeout));
    this.typewriterTimeouts = {};
  }

  addCompletionMessageWithTypewriter(message: string): void {
    const messageId = `ai-completion-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    const completionMessage: ChatMessage = {
      id: messageId,
      text: '',
      type: 'ai',
      timestamp: Date.now(),
      isTyping: true
    };

    this.addMessage(completionMessage);
    this.startTypewriterEffectForMessage(message, messageId);
  }

  generateRegenerationSessionId(): string {
    this.regenerationSessionCounter++;
    return `regen-session-${Date.now()}-${this.regenerationSessionCounter}-${Math.random().toString(36).substring(2, 11)}`;
  }

  createRegenerationSession(sessionId: string, data: any): void {
    this.activeRegenerationSessions.set(sessionId, {
      ...data,
      createdAt: Date.now(),
      isActive: true
    });
  }

  getRegenerationSession(sessionId: string): any {
    return this.activeRegenerationSessions.get(sessionId);
  }

  updateRegenerationSession(sessionId: string, updates: any): void {
    const existingSession = this.activeRegenerationSessions.get(sessionId);
    if (existingSession) {
      this.activeRegenerationSessions.set(sessionId, {
        ...existingSession,
        ...updates,
        updatedAt: Date.now()
      });
    }
  }

  cleanupRegenerationSession(sessionId: string): void {
    if (this.activeRegenerationSessions.has(sessionId)) {
      const session = this.activeRegenerationSessions.get(sessionId);
      if (session) {
        if (session.timeoutId) {
          clearTimeout(session.timeoutId);
        }
        if (session.intervalId) {
          clearInterval(session.intervalId);
        }
      }
      this.activeRegenerationSessions.delete(sessionId);
    }
  }

  cleanupAllRegenerationSessions(): void {
    for (const sessionId of this.activeRegenerationSessions.keys()) {
      this.cleanupRegenerationSession(sessionId);
    }
  }

  validateMessageStructure(message: any): boolean {
    return message &&
           typeof message.id === 'string' &&
           typeof message.text === 'string' &&
           typeof message.type === 'string' &&
           ['user', 'ai', 'system'].includes(message.type) &&
           typeof message.timestamp === 'number';
  }

  formatChatMessage(text: string, type: 'user' | 'ai' | 'system' = 'ai'): ChatMessage {
    return {
      id: `msg-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      text: text,
      type: type,
      timestamp: Date.now(),
      isTyping: false
    };
  }

  private messageQueue: ChatMessage[] = [];
  private isProcessingQueue = false;

  addMessageToQueue(message: ChatMessage): void {
    this.messageQueue.push(message);
    this.processMessageQueue();
  }

  private processMessageQueue(): void {
    if (this.isProcessingQueue || this.messageQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;
    
    const processNext = () => {
      if (this.messageQueue.length > 0) {
        const message = this.messageQueue.shift()!;
        this.addMessage(message);
        
        setTimeout(processNext, 100);
      } else {
        this.isProcessingQueue = false;
      }
    };

    processNext();
  }

  clearChatHistory(): void {
    this.lightMessages$.next([]);
    this.clearAllTypewriterTimeouts();
    this.cleanupAllRegenerationSessions();
    this.introMessageStates.clear();
    this.currentActiveMessageId = null;
  }

  exportChatHistory(): ChatMessage[] {
    return this.deepClone(this.lightMessages$.value);
  }

  importChatHistory(messages: ChatMessage[]): void {
    if (Array.isArray(messages) && messages.every(msg => this.validateMessageStructure(msg))) {
      this.lightMessages$.next(this.deepClone(messages));
    }
  }

  private deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj));
  }

  private typingIndicators = new Map<string, boolean>();

  setTypingIndicator(userId: string, isTyping: boolean): void {
    this.typingIndicators.set(userId, isTyping);
  }

  getTypingIndicator(userId: string): boolean {
    return this.typingIndicators.get(userId) || false;
  }

  clearAllTypingIndicators(): void {
    this.typingIndicators.clear();
  }
}
