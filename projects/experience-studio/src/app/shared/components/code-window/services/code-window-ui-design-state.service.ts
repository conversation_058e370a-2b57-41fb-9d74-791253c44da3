import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { UIDesignNode } from './ui-design-node.service';
import { MobilePage } from '../../mobile-frame/mobile-frame.component';

export interface IntroMessageState {
  isLoading: boolean;
  text: string;
  isTyping: boolean;
  hasError: boolean;
  shouldReplaceText: boolean;
  introAPICompleted: boolean;
  mainAPIInProgress: boolean;
  showLoadingIndicator: boolean;
  loadingPhase: 'intro' | 'main' | 'completed' | 'error';
  messageType: 'generation' | 'regeneration';
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowUIDesignStateService {
  // UI Design mode state
  private isUIDesignMode$ = new BehaviorSubject<boolean>(false);
  private uiDesignNodes$ = new BehaviorSubject<UIDesignNode[]>([]);
  private selectedUIDesignNode$ = new BehaviorSubject<UIDesignNode | null>(null);
  private isUIDesignFullScreenOpen$ = new BehaviorSubject<boolean>(false);
  private uiDesignViewMode$ = new BehaviorSubject<'mobile' | 'web'>('mobile');
  private isUIDesignModalFullScreen$ = new BehaviorSubject<boolean>(false);
  private isUIDesignCodeViewerOpen$ = new BehaviorSubject<boolean>(false);
  
  // Generation state
  private isUIDesignGenerating$ = new BehaviorSubject<boolean>(false);
  private uiDesignError$ = new BehaviorSubject<string | null>(null);
  private uiDesignApiInProgress$ = new BehaviorSubject<boolean>(false);
  private isWireframeGenerationComplete$ = new BehaviorSubject<boolean>(false);
  private isUIDesignRegenerating$ = new BehaviorSubject<boolean>(false);
  private uiDesignLoadingNodes$ = new BehaviorSubject<UIDesignNode[]>([]);
  private isUIDesignLoading$ = new BehaviorSubject<boolean>(false);
  
  // Canvas state
  private showCanvasTooltip$ = new BehaviorSubject<boolean>(true);
  private isEditingUIDesign$ = new BehaviorSubject<boolean>(false);
  
  // Overview state
  private showUIDesignOverviewTab$ = new BehaviorSubject<boolean>(false);
  private uiDesignPages$ = new BehaviorSubject<MobilePage[]>([]);
  private currentUIDesignPageIndex$ = new BehaviorSubject<number>(0);

  // Intro message state
  private introMessageState$ = new BehaviorSubject<IntroMessageState>({
    isLoading: false,
    text: '',
    isTyping: false,
    hasError: false,
    shouldReplaceText: false,
    introAPICompleted: false,
    mainAPIInProgress: false,
    showLoadingIndicator: false,
    loadingPhase: 'intro',
    messageType: 'generation',
  });

  // Public observables
  readonly isUIDesignMode = this.isUIDesignMode$.asObservable();
  readonly uiDesignNodes = this.uiDesignNodes$.asObservable();
  readonly selectedUIDesignNode = this.selectedUIDesignNode$.asObservable();
  readonly isUIDesignFullScreenOpen = this.isUIDesignFullScreenOpen$.asObservable();
  readonly uiDesignViewMode = this.uiDesignViewMode$.asObservable();
  readonly isUIDesignModalFullScreen = this.isUIDesignModalFullScreen$.asObservable();
  readonly isUIDesignCodeViewerOpen = this.isUIDesignCodeViewerOpen$.asObservable();
  readonly isUIDesignGenerating = this.isUIDesignGenerating$.asObservable();
  readonly uiDesignError = this.uiDesignError$.asObservable();
  readonly uiDesignApiInProgress = this.uiDesignApiInProgress$.asObservable();
  readonly isWireframeGenerationComplete = this.isWireframeGenerationComplete$.asObservable();
  readonly isUIDesignRegenerating = this.isUIDesignRegenerating$.asObservable();
  readonly uiDesignLoadingNodes = this.uiDesignLoadingNodes$.asObservable();
  readonly isUIDesignLoading = this.isUIDesignLoading$.asObservable();
  readonly showCanvasTooltip = this.showCanvasTooltip$.asObservable();
  readonly isEditingUIDesign = this.isEditingUIDesign$.asObservable();
  readonly showUIDesignOverviewTab = this.showUIDesignOverviewTab$.asObservable();
  readonly uiDesignPages = this.uiDesignPages$.asObservable();
  readonly currentUIDesignPageIndex = this.currentUIDesignPageIndex$.asObservable();
  readonly introMessageState = this.introMessageState$.asObservable();

  // State update methods
  setUIDesignMode(mode: boolean): void {
    this.isUIDesignMode$.next(mode);
  }

  getUIDesignMode(): boolean {
    return this.isUIDesignMode$.value;
  }

  setUIDesignNodes(nodes: UIDesignNode[]): void {
    this.uiDesignNodes$.next(nodes);
  }

  getUIDesignNodes(): UIDesignNode[] {
    return this.uiDesignNodes$.value;
  }

  setSelectedUIDesignNode(node: UIDesignNode | null): void {
    this.selectedUIDesignNode$.next(node);
  }

  getSelectedUIDesignNode(): UIDesignNode | null {
    return this.selectedUIDesignNode$.value;
  }

  setUIDesignFullScreenOpen(open: boolean): void {
    this.isUIDesignFullScreenOpen$.next(open);
  }

  setUIDesignViewMode(mode: 'mobile' | 'web'): void {
    this.uiDesignViewMode$.next(mode);
  }

  setUIDesignModalFullScreen(fullScreen: boolean): void {
    this.isUIDesignModalFullScreen$.next(fullScreen);
  }

  setUIDesignCodeViewerOpen(open: boolean): void {
    this.isUIDesignCodeViewerOpen$.next(open);
  }

  setUIDesignGenerating(generating: boolean): void {
    this.isUIDesignGenerating$.next(generating);
  }

  setUIDesignError(error: string | null): void {
    this.uiDesignError$.next(error);
  }

  setUIDesignApiInProgress(inProgress: boolean): void {
    this.uiDesignApiInProgress$.next(inProgress);
  }

  setWireframeGenerationComplete(complete: boolean): void {
    this.isWireframeGenerationComplete$.next(complete);
  }

  setUIDesignRegenerating(regenerating: boolean): void {
    this.isUIDesignRegenerating$.next(regenerating);
  }

  setUIDesignLoadingNodes(nodes: UIDesignNode[]): void {
    this.uiDesignLoadingNodes$.next(nodes);
  }

  getUIDesignLoadingNodes(): UIDesignNode[] {
    return this.uiDesignLoadingNodes$.value;
  }

  setUIDesignLoading(loading: boolean): void {
    this.isUIDesignLoading$.next(loading);
  }

  setShowCanvasTooltip(show: boolean): void {
    this.showCanvasTooltip$.next(show);
  }

  setEditingUIDesign(editing: boolean): void {
    this.isEditingUIDesign$.next(editing);
  }

  setShowUIDesignOverviewTab(show: boolean): void {
    this.showUIDesignOverviewTab$.next(show);
  }

  setUIDesignPages(pages: MobilePage[]): void {
    this.uiDesignPages$.next(pages);
  }

  getUIDesignPages(): MobilePage[] {
    return this.uiDesignPages$.value;
  }

  setCurrentUIDesignPageIndex(index: number): void {
    this.currentUIDesignPageIndex$.next(index);
  }

  getCurrentUIDesignPageIndex(): number {
    return this.currentUIDesignPageIndex$.value;
  }

  setIntroMessageState(state: Partial<IntroMessageState>): void {
    const currentState = this.introMessageState$.value;
    this.introMessageState$.next({ ...currentState, ...state });
  }

  getIntroMessageState(): IntroMessageState {
    return this.introMessageState$.value;
  }

  // Helper methods
  clearAllLoadingNodes(): void {
    this.uiDesignLoadingNodes$.next([]);
    
    const currentNodes = this.uiDesignNodes$.value;
    const nonLoadingNodes = currentNodes.filter(
      node =>
        !node.data.isLoading &&
        !node.id.startsWith('loading-') &&
        node.id !== 'ui-design-loading-node'
    );
    this.uiDesignNodes$.next(nonLoadingNodes);
  }

  addUIDesignNode(node: UIDesignNode): void {
    const currentNodes = this.uiDesignNodes$.value;
    this.uiDesignNodes$.next([...currentNodes, node]);
  }

  removeUIDesignNode(nodeId: string): void {
    const currentNodes = this.uiDesignNodes$.value;
    const filteredNodes = currentNodes.filter(node => node.id !== nodeId);
    this.uiDesignNodes$.next(filteredNodes);
  }

  updateUIDesignNode(nodeId: string, updates: Partial<UIDesignNode>): void {
    const currentNodes = this.uiDesignNodes$.value;
    const updatedNodes = currentNodes.map(node => 
      node.id === nodeId ? { ...node, ...updates } : node
    );
    this.uiDesignNodes$.next(updatedNodes);
  }

  // Reset methods
  reset(): void {
    this.isUIDesignMode$.next(false);
    this.uiDesignNodes$.next([]);
    this.selectedUIDesignNode$.next(null);
    this.isUIDesignFullScreenOpen$.next(false);
    this.uiDesignViewMode$.next('mobile');
    this.isUIDesignModalFullScreen$.next(false);
    this.isUIDesignCodeViewerOpen$.next(false);
    this.isUIDesignGenerating$.next(false);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);
    this.isWireframeGenerationComplete$.next(false);
    this.isUIDesignRegenerating$.next(false);
    this.uiDesignLoadingNodes$.next([]);
    this.isUIDesignLoading$.next(false);
    this.showCanvasTooltip$.next(true);
    this.isEditingUIDesign$.next(false);
    this.showUIDesignOverviewTab$.next(false);
    this.uiDesignPages$.next([]);
    this.currentUIDesignPageIndex$.next(0);
    this.introMessageState$.next({
      isLoading: false,
      text: '',
      isTyping: false,
      hasError: false,
      shouldReplaceText: false,
      introAPICompleted: false,
      mainAPIInProgress: false,
      showLoadingIndicator: false,
      loadingPhase: 'intro',
      messageType: 'generation',
    });
  }

  resetUIDesignGeneration(): void {
    this.isUIDesignGenerating$.next(false);
    this.uiDesignError$.next(null);
    this.uiDesignApiInProgress$.next(false);
    this.isUIDesignRegenerating$.next(false);
    this.clearAllLoadingNodes();
  }
}
