import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import { UIDesignNode } from './ui-design-node.service';
import { CodeWindowUIDesignStateService } from './code-window-ui-design-state.service';
import { GenerateUIDesignService } from '../../../services/generate-ui-design.service';
import { ToastService } from '../../../services/toast.service';

export interface UIDesignGenerationOptions {
  userRequest: string;
  sessionId: string;
  projectId: string;
  jobId: string;
  regenerationType?: 'direct' | 'sequential';
  imageDataUri?: string; // Optional image data URI
  applicationTarget?: 'mobile' | 'web'; // Optional application target
  docsContent?: string; // Optional text content from .txt files (separate from userRequest)
}

export interface UIDesignGenerationResult {
  success: boolean;
  nodes?: UIDesignNode[];
  pages?: any[];
  error?: any;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowUIDesignGenerationService {
  private generationInProgress$ = new BehaviorSubject<boolean>(false);
  private currentGenerationId: string | null = null;

  // Injected services using Angular 19+ patterns
  private readonly generateUIDesignService = inject(GenerateUIDesignService);
  private readonly toastService = inject(ToastService);

  // Events
  private generationStarted$ = new Subject<UIDesignGenerationOptions>();
  private generationCompleted$ = new Subject<UIDesignGenerationResult>();
  private generationFailed$ = new Subject<{ error: any; options: UIDesignGenerationOptions }>();

  // Public observables
  readonly generationInProgress = this.generationInProgress$.asObservable();
  readonly generationStarted = this.generationStarted$.asObservable();
  readonly generationCompleted = this.generationCompleted$.asObservable();
  readonly generationFailed = this.generationFailed$.asObservable();

  constructor(
    private uiDesignStateService: CodeWindowUIDesignStateService
  ) {}

  // Start UI Design generation
  startUIDesignGeneration(options: UIDesignGenerationOptions): void {
    if (this.generationInProgress$.value) {

      return;
    }

    this.currentGenerationId = this.generateSessionId();
    this.generationInProgress$.next(true);

    // Update state service
    this.uiDesignStateService.setUIDesignGenerating(true);
    this.uiDesignStateService.setUIDesignError(null);
    this.uiDesignStateService.setUIDesignApiInProgress(false);

    // Clear existing nodes and create loading nodes
    this.uiDesignStateService.setUIDesignNodes([]);
    this.createLoadingNodes();

    // Emit started event
    this.generationStarted$.next(options);

    // Start API call
    this.makeUIDesignAPICall(options);
  }

  // Create loading nodes for UI Design generation
  createLoadingNodes(): void {
    const loadingNodes: UIDesignNode[] = [
      {
        id: 'ui-design-loading-node',
        type: 'ui-design',
        position: { x: 100, y: 100 },
        data: {
          title: 'Generating UI Design...',
          htmlContent: '' as any, // Will be set by sanitizer
          rawContent: 'Please wait while we generate your UI design.',
          width: 300,
          height: 200,
          isLoading: true,
          loadingMessage: 'Analyzing your requirements and generating wireframes...'
        },
        selected: false,
        dragging: false,
        visible: true
      }
    ];

    this.uiDesignStateService.setUIDesignNodes(loadingNodes);
  }

  // Make API call for UI Design generation
  private makeUIDesignAPICall(options: UIDesignGenerationOptions): void {
    this.uiDesignStateService.setUIDesignApiInProgress(true);
    this.uiDesignStateService.setUIDesignError(null);

    // Intro message state is managed by the UI design state service

    // Check if there's an image in the options
    const images = options.imageDataUri ? [options.imageDataUri] : [];

    // Build API request with image data and docs content (separate from prompt)
    const apiRequest = this.generateUIDesignService.buildAPIRequest(images, options.docsContent);

    // Make the API call
    this.generateUIDesignService.generateUIDesign(apiRequest).subscribe({
      next: (response) => {
        this.handleUIDesignSuccess(response, options);
      },
      error: (error) => {
        this.handleUIDesignFailure(error, options);
      }
    });
  }

  // Handle successful UI Design generation
  private handleUIDesignSuccess(response: any, _options: UIDesignGenerationOptions): void {
    this.generationInProgress$.next(false);

    // Update state service
    this.uiDesignStateService.setUIDesignGenerating(false);
    this.uiDesignStateService.setUIDesignApiInProgress(false);
    this.uiDesignStateService.setUIDesignError(null);
    this.uiDesignStateService.setUIDesignLoading(false);

    // Process the response
    const result = this.processUIDesignResponse(response);

    // Emit completion event
    this.generationCompleted$.next({
      success: true,
      nodes: result.nodes,
      pages: result.pages
    });

    this.currentGenerationId = null;
  }

  // Handle failed UI Design generation
  private handleUIDesignFailure(error: any, options: UIDesignGenerationOptions): void {
    this.generationInProgress$.next(false);

    // Update state service
    this.uiDesignStateService.setUIDesignGenerating(false);
    this.uiDesignStateService.setUIDesignApiInProgress(false);
    this.uiDesignStateService.setWireframeGenerationComplete(false);
    this.uiDesignStateService.setUIDesignLoading(false);

    const errorMessage = error?.message || error?.error?.message || 'Wireframe generation failed';
    this.uiDesignStateService.setUIDesignError(errorMessage);

    // Create error node
    this.createErrorNode(errorMessage);

    // Show toast notification
    this.toastService.error(errorMessage);

    // Emit failure event
    this.generationFailed$.next({ error, options });

    this.currentGenerationId = null;
  }

  // Process UI Design response
  private processUIDesignResponse(response: any): { nodes: UIDesignNode[]; pages: any[] } {
    const nodes: UIDesignNode[] = [];
    const pages: any[] = [];

    // Handle new API response structure with wireframes array
    if (response.wireframes && Array.isArray(response.wireframes)) {
      // Store project_id and commit_hash if available
      if (response.project_id) {
        this.generateUIDesignService.setProjectId(response.project_id);
      }
      if (response.commit_hash) {
        this.generateUIDesignService.setCommitHash(response.commit_hash);
      }

      response.wireframes.forEach((wireframe: any, index: number) => {
        const node: UIDesignNode = {
          id: `ui-design-node-${index}`,
          type: 'ui-design',
          position: { x: 50 + (index * 320), y: 50 },
          data: {
            title: wireframe.fileName || `Page ${index + 1}`,
            htmlContent: '' as any, // Will be set by sanitizer
            rawContent: wireframe.content || '',
            width: 300,
            height: 400,
            isLoading: false
          },
          selected: false,
          dragging: false,
          visible: true
        };

        nodes.push(node);

        // Also add to pages array for overview tab
        pages.push({
          fileName: wireframe.fileName,
          content: wireframe.content
        });
      });
    }
    // Fallback for legacy response format (backward compatibility)
    else if (Array.isArray(response)) {
      response.forEach((item: any, index: number) => {
        const node: UIDesignNode = {
          id: `ui-design-node-${index}`,
          type: 'ui-design',
          position: { x: 50 + (index * 320), y: 50 },
          data: {
            title: item.fileName || item.pageName || `Page ${index + 1}`,
            htmlContent: '' as any, // Will be set by sanitizer
            rawContent: item.content || '',
            width: 300,
            height: 400,
            isLoading: false
          },
          selected: false,
          dragging: false,
          visible: true
        };

        nodes.push(node);

        // Also add to pages array for overview tab
        pages.push({
          fileName: item.fileName || item.pageName,
          content: item.content
        });
      });
    }

    if (response.pages && Array.isArray(response.pages)) {
      pages.push(...response.pages);
    }

    // Update state with processed data
    this.uiDesignStateService.setUIDesignNodes(nodes);

    if (pages.length > 0) {
      this.uiDesignStateService.setUIDesignPages(pages);
      this.uiDesignStateService.setCurrentUIDesignPageIndex(0);
      this.uiDesignStateService.setShowUIDesignOverviewTab(true);
    }

    // Set UI Design mode and complete generation
    this.uiDesignStateService.setUIDesignMode(true);
    this.uiDesignStateService.setWireframeGenerationComplete(true);

    return { nodes, pages };
  }

  // Create error node
  private createErrorNode(errorMessage: string): void {
    const errorNode: UIDesignNode = {
      id: 'ui-design-error-node',
      type: 'ui-design',
      position: { x: 100, y: 100 },
      data: {
        title: 'Generation Failed',
        htmlContent: '' as any, // Will be set by sanitizer
        rawContent: errorMessage,
        width: 300,
        height: 200,
        isLoading: false
      },
      selected: false,
      dragging: false,
      visible: true
    };

    this.uiDesignStateService.setUIDesignNodes([errorNode]);
  }

  // Generate session ID
  private generateSessionId(): string {
    return `ui_design_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  // Check if generation is in progress
  isGenerationInProgress(): boolean {
    return this.generationInProgress$.value;
  }

  // Get current generation ID
  getCurrentGenerationId(): string | null {
    return this.currentGenerationId;
  }

  // Cancel current generation
  cancelGeneration(): void {
    if (this.generationInProgress$.value) {
      this.generationInProgress$.next(false);
      this.uiDesignStateService.setUIDesignGenerating(false);
      this.uiDesignStateService.setUIDesignApiInProgress(false);
      this.uiDesignStateService.setUIDesignLoading(false);

      this.currentGenerationId = null;

      this.toastService.info('UI Design generation cancelled');
    }
  }

  // Reset service state
  reset(): void {
    this.generationInProgress$.next(false);
    this.currentGenerationId = null;
  }

  // Cleanup method for component destruction
  cleanup(): void {
    this.generationStarted$.complete();
    this.generationCompleted$.complete();
    this.generationFailed$.complete();
  }
}
