import { Injectable, ChangeDetectorRef } from '@angular/core';
import { BehaviorSubject, Observable, Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { FileModel } from '../../code-viewer/code-viewer.component';
import { CodeWindowFileOperationsService } from './code-window-file-operations.service';
import { CodeWindowTabManagementService } from './code-window-tab-management.service';

export interface TemplateFile {
  name: string;
  fileName?: string;
  content: string;
  type?: string;
}

export interface TemplateValidationResult {
  isValid: boolean;
  reason?: string;
}

export interface TemplateProcessingResult {
  success: boolean;
  validFiles: FileModel[];
  invalidFiles: Array<{ file: TemplateFile; reason: string }>;
  fileTypesSummary: Record<string, number>;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowTemplateLoadingService {
  private templateProcessingState$ = new BehaviorSubject<{
    isProcessing: boolean;
    lastResult: TemplateProcessingResult | null;
  }>({
    isProcessing: false,
    lastResult: null
  });

  // Events
  private templateProcessed$ = new Subject<TemplateProcessingResult>();
  private templateProcessingFailed$ = new Subject<{ error: any; files: TemplateFile[] }>();

  // Public observables
  readonly templateProcessingState = this.templateProcessingState$.asObservable();
  readonly templateProcessed = this.templateProcessed$.asObservable();
  readonly templateProcessingFailed = this.templateProcessingFailed$.asObservable();

  constructor(
    private fileOperationsService: CodeWindowFileOperationsService,
    private tabManagementService: CodeWindowTabManagementService
  ) {}

  // Process template files
  processTemplateFiles(templateFiles: TemplateFile[]): TemplateProcessingResult {
    this.templateProcessingState$.next({
      isProcessing: true,
      lastResult: null
    });

    try {
      if (!templateFiles || templateFiles.length === 0) {
        const result: TemplateProcessingResult = {
          success: false,
          validFiles: [],
          invalidFiles: [],
          fileTypesSummary: {}
        };
        
        this.templateProcessingState$.next({
          isProcessing: false,
          lastResult: result
        });
        
        return result;
      }

      const fileModels: FileModel[] = [];
      const invalidFiles: Array<{ file: TemplateFile; reason: string }> = [];

      // Process each file
      for (const file of templateFiles) {
        try {
          const validationResult = this.validateTemplateFile(file);

          if (validationResult.isValid) {
            const fileModel: FileModel = {
              name: file.name,
              type: 'file' as const,
              content: file.content || '',
              fileName: file.fileName || file.name
            };
            fileModels.push(fileModel);
          } else {
            invalidFiles.push({
              file,
              reason: validationResult.reason || 'Unknown validation error'
            });
          }
        } catch (fileError) {
          invalidFiles.push({
            file,
            reason: `Processing error: ${fileError instanceof Error ? fileError.message : String(fileError)}`
          });
        }
      }

      // Generate file types summary
      const fileTypesSummary = this.getFileTypesSummary(fileModels);

      const result: TemplateProcessingResult = {
        success: fileModels.length > 0,
        validFiles: fileModels,
        invalidFiles,
        fileTypesSummary
      };

      // Update state
      this.templateProcessingState$.next({
        isProcessing: false,
        lastResult: result
      });

      // Emit event
      this.templateProcessed$.next(result);

      return result;

    } catch (error) {
      const result: TemplateProcessingResult = {
        success: false,
        validFiles: [],
        invalidFiles: [],
        fileTypesSummary: {}
      };

      this.templateProcessingState$.next({
        isProcessing: false,
        lastResult: result
      });

      this.templateProcessingFailed$.next({ error, files: templateFiles });

      return result;
    }
  }

  // Validate template file
  validateTemplateFile(file: TemplateFile): TemplateValidationResult {
    if (!file) {
      return { isValid: false, reason: 'File object is null or undefined' };
    }

    if (!file.name && !file.fileName) {
      return { isValid: false, reason: 'File has no name or fileName property' };
    }

    const fileName = file.name || file.fileName;
    if (typeof fileName !== 'string' || fileName.trim() === '') {
      return { isValid: false, reason: 'File name is not a valid string' };
    }

    if (file.content === null || file.content === undefined) {
      return { isValid: false, reason: 'File content is null or undefined' };
    }

    if (typeof file.content !== 'string') {
      return { isValid: false, reason: 'File content is not a string' };
    }

    if (fileName.length > 255) {
      return { isValid: false, reason: 'File name is too long (>255 characters)' };
    }

    if (file.content.length > 50 * 1024 * 1024) {
      return { isValid: false, reason: 'File content is too large (>50MB)' };
    }

    return { isValid: true };
  }

  // Get file types summary
  getFileTypesSummary(files: FileModel[]): Record<string, number> {
    const summary: Record<string, number> = {};

    files.forEach(file => {
      const fileName = file.fileName || file.name || '';
      let extension = fileName.split('.').pop()?.toLowerCase() || 'no-extension';

      const extensionGroups: Record<string, string> = {
        'js': 'javascript',
        'jsx': 'javascript',
        'ts': 'typescript',
        'tsx': 'typescript',
        'css': 'styles',
        'scss': 'styles',
        'sass': 'styles',
        'less': 'styles',
        'html': 'markup',
        'htm': 'markup',
        'xml': 'markup',
        'json': 'config',
        'yaml': 'config',
        'yml': 'config',
        'toml': 'config',
        'ini': 'config',
        'md': 'documentation',
        'txt': 'documentation',
        'rst': 'documentation'
      };

      const category = extensionGroups[extension] || extension;
      summary[category] = (summary[category] || 0) + 1;
    });

    return summary;
  }

  // Handle successful template loading
  handleTemplateLoadingSuccess(
    result: TemplateProcessingResult,
    callbacks: {
      setCodeTabEnabled: (message: string) => void;
      setCodeGenerationComplete: () => void;
      setViewingSeedProjectTemplate: () => void;
      setUserSelectedTab: () => void;
      handleAutomaticCodeTabSwitch: () => void;
      detectChanges: () => void;
      markForCheck: () => void;
    }
  ): void {
    if (result.validFiles.length > 0) {
      try {
        // Process files through file operations service
        this.fileOperationsService.processCodeFromService(result.validFiles);

        // Update component state through callbacks
        callbacks.setCodeGenerationComplete();
        callbacks.setViewingSeedProjectTemplate();
        callbacks.setCodeTabEnabled('View seed project template');
        callbacks.handleAutomaticCodeTabSwitch();
        callbacks.setUserSelectedTab();
        callbacks.detectChanges();

        callbacks.markForCheck();
      } catch (loadingError) {
        throw new Error('Failed to load files into Monaco Editor');
      }
    }
  }

  // Handle template loading failure
  handleTemplateLoadingFailure(
    error: any,
    callbacks: {
      setCodeTabDisabled: (message: string) => void;
    }
  ): void {
    callbacks.setCodeTabDisabled('Code will be available during build phase');
  }

  // Check if processing is in progress
  isProcessing(): boolean {
    return this.templateProcessingState$.value.isProcessing;
  }

  // Get last processing result
  getLastResult(): TemplateProcessingResult | null {
    return this.templateProcessingState$.value.lastResult;
  }

  // Reset service state
  reset(): void {
    this.templateProcessingState$.next({
      isProcessing: false,
      lastResult: null
    });
  }

  // Cleanup method for component destruction
  cleanup(): void {
    this.templateProcessed$.complete();
    this.templateProcessingFailed$.complete();
  }
}
