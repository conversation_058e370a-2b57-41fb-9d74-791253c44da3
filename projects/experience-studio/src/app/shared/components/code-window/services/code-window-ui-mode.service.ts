import { Injectable, signal, computed, inject, DestroyRef, NgZone } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CodeWindowUIModeService {
  private destroyRef = inject(DestroyRef);
  private ngZone = inject(NgZone);

  readonly currentMode = signal<'code-generation' | 'ui-design' | 'hybrid'>('code-generation');
  readonly isUIDesignMode = signal<boolean>(false);
  readonly isCodeGenerationMode = signal<boolean>(true);
  readonly isHybridMode = signal<boolean>(false);
  readonly isModeTransitioning = signal<boolean>(false);

  private isUIDesignMode$ = new BehaviorSubject<boolean>(false);
  private isUIDesignGenerating$ = new BehaviorSubject<boolean>(false);
  private isUIDesignRegenerating$ = new BehaviorSubject<boolean>(false);
  private uiDesignApiInProgress$ = new BehaviorSubject<boolean>(false);
  private isCodeGenerationLoading$ = new BehaviorSubject<boolean>(false);

  private canvasEnabled$ = new BehaviorSubject<boolean>(false);
  private nodeOperationsEnabled$ = new BehaviorSubject<boolean>(false);
  private designTokensEnabled$ = new BehaviorSubject<boolean>(false);
  private layoutAnalysisEnabled$ = new BehaviorSubject<boolean>(false);

  readonly isUIDesignModeObs = this.isUIDesignMode$.asObservable();
  readonly isUIDesignGenerating = this.isUIDesignGenerating$.asObservable();
  readonly isUIDesignRegenerating = this.isUIDesignRegenerating$.asObservable();
  readonly uiDesignApiInProgress = this.uiDesignApiInProgress$.asObservable();
  readonly isCodeGenerationLoading = this.isCodeGenerationLoading$.asObservable();

  readonly canvasEnabled = this.canvasEnabled$.asObservable();
  readonly nodeOperationsEnabled = this.nodeOperationsEnabled$.asObservable();
  readonly designTokensEnabled = this.designTokensEnabled$.asObservable();
  readonly layoutAnalysisEnabled = this.layoutAnalysisEnabled$.asObservable();

  readonly canSwitchModes = computed(() => !this.isModeTransitioning());
  readonly isAnyModeActive = computed(() =>
    this.isUIDesignMode() || this.isCodeGenerationMode() || this.isHybridMode()
  );
  readonly shouldShowUIDesignFeatures = computed(() =>
    this.isUIDesignMode() || this.isHybridMode()
  );
  readonly shouldShowCodeGenerationFeatures = computed(() =>
    this.isCodeGenerationMode() || this.isHybridMode()
  );

  constructor() {
    this.initializeDefaultMode();
  }

  setupUIDesignMode(): void {
    if (!this.canSwitchModes()) {
      return;
    }

    this.startModeTransition('ui-design');

    this.ngZone.run(() => {
      this.isUIDesignMode.set(true);
      this.isCodeGenerationMode.set(false);
      this.isHybridMode.set(false);
      this.currentMode.set('ui-design');

      this.isUIDesignMode$.next(true);
      this.isCodeGenerationLoading$.next(false);

      this.enableUIDesignFeatures();

      this.completeModeTransition();
    });
  }

  cleanupUIDesignMode(): void {
    this.ngZone.run(() => {
      this.isUIDesignMode.set(false);
      this.isCodeGenerationMode.set(true);
      this.currentMode.set('code-generation');

      this.isUIDesignMode$.next(false);
      this.isUIDesignGenerating$.next(false);
      this.isUIDesignRegenerating$.next(false);
      this.uiDesignApiInProgress$.next(false);

      this.disableUIDesignFeatures();
    });
  }

  setupCodeGenerationMode(): void {
    if (!this.canSwitchModes()) {
      return;
    }

    this.startModeTransition('code-generation');

    this.ngZone.run(() => {
      this.isUIDesignMode.set(false);
      this.isCodeGenerationMode.set(true);
      this.isHybridMode.set(false);
      this.currentMode.set('code-generation');

      this.isUIDesignMode$.next(false);
      this.isCodeGenerationLoading$.next(true);

      this.disableUIDesignFeatures();

      this.completeModeTransition();
    });
  }

  setupHybridMode(): void {
    if (!this.canSwitchModes()) {
      return;
    }

    this.startModeTransition('hybrid');

    this.ngZone.run(() => {
      this.isUIDesignMode.set(true);
      this.isCodeGenerationMode.set(true);
      this.isHybridMode.set(true);
      this.currentMode.set('hybrid');

      this.isUIDesignMode$.next(true);
      this.isCodeGenerationLoading$.next(true);

      this.enableUIDesignFeatures();

      this.completeModeTransition();
    });
  }

  startUIDesignGeneration(): void {
    this.isUIDesignGenerating$.next(true);
    this.uiDesignApiInProgress$.next(true);
  }

  stopUIDesignGeneration(): void {
    this.isUIDesignGenerating$.next(false);
    this.uiDesignApiInProgress$.next(false);
  }

  startUIDesignRegeneration(): void {
    this.isUIDesignRegenerating$.next(true);
    this.uiDesignApiInProgress$.next(true);
  }

  stopUIDesignRegeneration(): void {
    this.isUIDesignRegenerating$.next(false);
    this.uiDesignApiInProgress$.next(false);
  }

  startCodeGenerationLoading(): void {
    this.isCodeGenerationLoading$.next(true);
  }

  stopCodeGenerationLoading(): void {
    this.isCodeGenerationLoading$.next(false);
  }

  private enableUIDesignFeatures(): void {
    this.canvasEnabled$.next(true);
    this.nodeOperationsEnabled$.next(true);
    this.designTokensEnabled$.next(true);
    this.layoutAnalysisEnabled$.next(true);
  }

  private disableUIDesignFeatures(): void {
    this.canvasEnabled$.next(false);
    this.nodeOperationsEnabled$.next(false);
    this.designTokensEnabled$.next(false);
    this.layoutAnalysisEnabled$.next(false);
  }

  private startModeTransition(targetMode: string): void {
    this.isModeTransitioning.set(true);
  }

  private completeModeTransition(): void {
    setTimeout(() => {
      this.isModeTransitioning.set(false);
    }, 300);
  }

  private initializeDefaultMode(): void {
    this.setupCodeGenerationMode();
  }

  resetToDefaultMode(): void {
    this.cleanupUIDesignMode();
    this.setupCodeGenerationMode();
  }

  getCurrentModeState(): {
    currentMode: string;
    signals: {
      isUIDesignMode: boolean;
      isCodeGenerationMode: boolean;
      isHybridMode: boolean;
      isModeTransitioning: boolean;
    };
    behaviorSubjects: {
      isUIDesignMode: boolean;
      isUIDesignGenerating: boolean;
      isUIDesignRegenerating: boolean;
      uiDesignApiInProgress: boolean;
      isCodeGenerationLoading: boolean;
    };
    features: {
      canvasEnabled: boolean;
      nodeOperationsEnabled: boolean;
      designTokensEnabled: boolean;
      layoutAnalysisEnabled: boolean;
    };
  } {
    return {
      currentMode: this.currentMode(),
      signals: {
        isUIDesignMode: this.isUIDesignMode(),
        isCodeGenerationMode: this.isCodeGenerationMode(),
        isHybridMode: this.isHybridMode(),
        isModeTransitioning: this.isModeTransitioning(),
      },
      behaviorSubjects: {
        isUIDesignMode: this.isUIDesignMode$.value,
        isUIDesignGenerating: this.isUIDesignGenerating$.value,
        isUIDesignRegenerating: this.isUIDesignRegenerating$.value,
        uiDesignApiInProgress: this.uiDesignApiInProgress$.value,
        isCodeGenerationLoading: this.isCodeGenerationLoading$.value,
      },
      features: {
        canvasEnabled: this.canvasEnabled$.value,
        nodeOperationsEnabled: this.nodeOperationsEnabled$.value,
        designTokensEnabled: this.designTokensEnabled$.value,
        layoutAnalysisEnabled: this.layoutAnalysisEnabled$.value,
      }
    };
  }
}
