import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface DesignToken {
  id: string;
  name: string;
  value: string;
  type: 'color' | 'typography' | 'spacing' | 'size';
  category: string;
  editable: boolean;
}

export interface DesignTokenEditState {
  isEditMode: boolean;
  editButtonText: string;
  hasUnsavedChanges: boolean;
  originalValues: Map<string, string>;
}

export interface DesignTokenLoadingState {
  isLoading: boolean;
  showAnalyzingAnimation: boolean;
  hasReceivedTokens: boolean;
  loadingMessage: string;
}

export interface ArtifactTypewriterState {
  visibleContent: string;
  isTyping: boolean;
  fullContent: string;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowArtifactsStateService {
  // Artifacts state
  private artifactsData$ = new BehaviorSubject<any[]>([]);
  private selectedArtifactFile$ = new BehaviorSubject<any>(null);
  private isArtifactsTabEnabled$ = new BehaviorSubject<boolean>(false);
  private isArtifactsTabEnabledWithLogs$ = new BehaviorSubject<boolean>(false);
  private isArtifactsTabVisible$ = new BehaviorSubject<boolean>(true);
  private loadedArtifacts$ = new BehaviorSubject<Set<string>>(new Set());
  private persistentArtifacts$ = new BehaviorSubject<Map<string, any>>(new Map());

  // Design system state
  private designSystemData$ = new BehaviorSubject<any>(null);
  private designTokens$ = new BehaviorSubject<DesignToken[]>([]);
  private hasDesignSystem$ = new BehaviorSubject<boolean>(false);
  
  // Design token edit state
  private designTokenEditState$ = new BehaviorSubject<DesignTokenEditState>({
    isEditMode: false,
    editButtonText: 'Edit',
    hasUnsavedChanges: false,
    originalValues: new Map<string, string>()
  });

  private designTokenLoadingState$ = new BehaviorSubject<DesignTokenLoadingState>({
    isLoading: false,
    showAnalyzingAnimation: false,
    hasReceivedTokens: false,
    loadingMessage: 'Analyzing Design Tokens...'
  });

  // Layout analysis state
  private layoutAnalyzedData$ = new BehaviorSubject<any[]>([]);
  private hasLayoutAnalyzed$ = new BehaviorSubject<boolean>(false);
  private isAnalyzingLayout$ = new BehaviorSubject<boolean>(true);
  private hasLayoutBeenDetected$ = new BehaviorSubject<boolean>(false);

  // Typewriter state
  private artifactTypewriterStates$ = new BehaviorSubject<{[key: string]: ArtifactTypewriterState}>({});
  private typewriterTimeouts$ = new BehaviorSubject<{[key: string]: any}>({});

  // Public observables
  readonly artifactsData = this.artifactsData$.asObservable();
  readonly selectedArtifactFile = this.selectedArtifactFile$.asObservable();
  readonly isArtifactsTabEnabled = this.isArtifactsTabEnabled$.asObservable();
  readonly isArtifactsTabEnabledWithLogs = this.isArtifactsTabEnabledWithLogs$.asObservable();
  readonly isArtifactsTabVisible = this.isArtifactsTabVisible$.asObservable();
  readonly loadedArtifacts = this.loadedArtifacts$.asObservable();
  readonly persistentArtifacts = this.persistentArtifacts$.asObservable();
  readonly designSystemData = this.designSystemData$.asObservable();
  readonly designTokens = this.designTokens$.asObservable();
  readonly hasDesignSystem = this.hasDesignSystem$.asObservable();
  readonly designTokenEditState = this.designTokenEditState$.asObservable();
  readonly designTokenLoadingState = this.designTokenLoadingState$.asObservable();
  readonly layoutAnalyzedData = this.layoutAnalyzedData$.asObservable();
  readonly hasLayoutAnalyzed = this.hasLayoutAnalyzed$.asObservable();
  readonly isAnalyzingLayout = this.isAnalyzingLayout$.asObservable();
  readonly hasLayoutBeenDetected = this.hasLayoutBeenDetected$.asObservable();
  readonly artifactTypewriterStates = this.artifactTypewriterStates$.asObservable();

  // Artifacts methods
  setArtifactsData(data: any[]): void {
    this.artifactsData$.next(data);
  }

  getArtifactsData(): any[] {
    return this.artifactsData$.value;
  }

  addArtifact(artifact: any): void {
    const currentData = this.artifactsData$.value;
    this.artifactsData$.next([...currentData, artifact]);
  }

  removeArtifact(artifactName: string): void {
    const currentData = this.artifactsData$.value;
    const filteredData = currentData.filter(artifact => artifact.name !== artifactName);
    this.artifactsData$.next(filteredData);
  }

  updateArtifact(artifactName: string, updates: any): void {
    const currentData = this.artifactsData$.value;
    const updatedData = currentData.map(artifact => 
      artifact.name === artifactName ? { ...artifact, ...updates } : artifact
    );
    this.artifactsData$.next(updatedData);
  }

  setSelectedArtifactFile(file: any): void {
    this.selectedArtifactFile$.next(file);
  }

  getSelectedArtifactFile(): any {
    return this.selectedArtifactFile$.value;
  }

  setArtifactsTabEnabled(enabled: boolean): void {
    this.isArtifactsTabEnabled$.next(enabled);
  }

  setArtifactsTabEnabledWithLogs(enabled: boolean): void {
    this.isArtifactsTabEnabledWithLogs$.next(enabled);
  }

  setArtifactsTabVisible(visible: boolean): void {
    this.isArtifactsTabVisible$.next(visible);
  }

  addLoadedArtifact(artifactName: string): void {
    const currentSet = this.loadedArtifacts$.value;
    const newSet = new Set(currentSet);
    newSet.add(artifactName);
    this.loadedArtifacts$.next(newSet);
  }

  clearLoadedArtifacts(): void {
    this.loadedArtifacts$.next(new Set());
  }

  setPersistentArtifact(key: string, artifact: any): void {
    const currentMap = this.persistentArtifacts$.value;
    const newMap = new Map(currentMap);
    newMap.set(key, {
      ...artifact,
      timestamp: Date.now(),
      persistent: true
    });
    this.persistentArtifacts$.next(newMap);
  }

  getPersistentArtifacts(): Map<string, any> {
    return this.persistentArtifacts$.value;
  }

  clearPersistentArtifacts(): void {
    this.persistentArtifacts$.next(new Map());
  }

  // Design system methods
  setDesignSystemData(data: any): void {
    this.designSystemData$.next(data);
  }

  getDesignSystemData(): any {
    return this.designSystemData$.value;
  }

  setDesignTokens(tokens: DesignToken[]): void {
    this.designTokens$.next(tokens);
  }

  getDesignTokens(): DesignToken[] {
    return this.designTokens$.value;
  }

  updateDesignToken(tokenId: string, updates: Partial<DesignToken>): void {
    const currentTokens = this.designTokens$.value;
    const updatedTokens = currentTokens.map(token => 
      token.id === tokenId ? { ...token, ...updates } : token
    );
    this.designTokens$.next(updatedTokens);
  }

  setHasDesignSystem(has: boolean): void {
    this.hasDesignSystem$.next(has);
  }

  setDesignTokenEditState(state: Partial<DesignTokenEditState>): void {
    const currentState = this.designTokenEditState$.value;
    this.designTokenEditState$.next({ ...currentState, ...state });
  }

  getDesignTokenEditState(): DesignTokenEditState {
    return this.designTokenEditState$.value;
  }

  setDesignTokenLoadingState(state: Partial<DesignTokenLoadingState>): void {
    const currentState = this.designTokenLoadingState$.value;
    this.designTokenLoadingState$.next({ ...currentState, ...state });
  }

  getDesignTokenLoadingState(): DesignTokenLoadingState {
    return this.designTokenLoadingState$.value;
  }

  // Layout analysis methods
  setLayoutAnalyzedData(data: any[]): void {
    this.layoutAnalyzedData$.next(data);
  }

  getLayoutAnalyzedData(): any[] {
    return this.layoutAnalyzedData$.value;
  }

  setHasLayoutAnalyzed(has: boolean): void {
    this.hasLayoutAnalyzed$.next(has);
  }

  setAnalyzingLayout(analyzing: boolean): void {
    this.isAnalyzingLayout$.next(analyzing);
  }

  setLayoutBeenDetected(detected: boolean): void {
    this.hasLayoutBeenDetected$.next(detected);
  }

  // Typewriter methods
  setArtifactTypewriterState(artifactName: string, state: ArtifactTypewriterState): void {
    const currentStates = this.artifactTypewriterStates$.value;
    this.artifactTypewriterStates$.next({
      ...currentStates,
      [artifactName]: state
    });
  }

  getArtifactTypewriterState(artifactName: string): ArtifactTypewriterState | undefined {
    return this.artifactTypewriterStates$.value[artifactName];
  }

  clearArtifactTypewriterStates(): void {
    this.artifactTypewriterStates$.next({});
  }

  setTypewriterTimeout(key: string, timeout: any): void {
    const currentTimeouts = this.typewriterTimeouts$.value;
    this.typewriterTimeouts$.next({
      ...currentTimeouts,
      [key]: timeout
    });
  }

  clearTypewriterTimeout(key: string): void {
    const currentTimeouts = this.typewriterTimeouts$.value;
    if (currentTimeouts[key]) {
      clearTimeout(currentTimeouts[key]);
      const { [key]: removed, ...remaining } = currentTimeouts;
      this.typewriterTimeouts$.next(remaining);
    }
  }

  clearAllTypewriterTimeouts(): void {
    const currentTimeouts = this.typewriterTimeouts$.value;
    Object.values(currentTimeouts).forEach(timeout => clearTimeout(timeout));
    this.typewriterTimeouts$.next({});
  }

  // Reset methods
  reset(): void {
    this.artifactsData$.next([]);
    this.selectedArtifactFile$.next(null);
    this.isArtifactsTabEnabled$.next(false);
    this.isArtifactsTabEnabledWithLogs$.next(false);
    this.isArtifactsTabVisible$.next(true);
    this.loadedArtifacts$.next(new Set());
    this.persistentArtifacts$.next(new Map());
    this.designSystemData$.next(null);
    this.designTokens$.next([]);
    this.hasDesignSystem$.next(false);
    this.designTokenEditState$.next({
      isEditMode: false,
      editButtonText: 'Edit',
      hasUnsavedChanges: false,
      originalValues: new Map<string, string>()
    });
    this.designTokenLoadingState$.next({
      isLoading: false,
      showAnalyzingAnimation: false,
      hasReceivedTokens: false,
      loadingMessage: 'Analyzing Design Tokens...'
    });
    this.layoutAnalyzedData$.next([]);
    this.hasLayoutAnalyzed$.next(false);
    this.isAnalyzingLayout$.next(true);
    this.hasLayoutBeenDetected$.next(false);
    this.clearArtifactTypewriterStates();
    this.clearAllTypewriterTimeouts();
  }

  resetArtifactsCompletely(): void {
    this.artifactsData$.next([]);
    this.loadedArtifacts$.next(new Set());
    this.hasLayoutAnalyzed$.next(false);
    this.hasDesignSystem$.next(false);
    this.persistentArtifacts$.next(new Map());
    this.isArtifactsTabVisible$.next(true);
    this.selectedArtifactFile$.next(null);
    this.layoutAnalyzedData$.next([]);
    this.designSystemData$.next(null);
    this.designTokens$.next([]);
  }
}
