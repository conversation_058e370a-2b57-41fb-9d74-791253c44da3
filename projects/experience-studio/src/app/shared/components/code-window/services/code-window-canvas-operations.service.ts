import { Injectable, signal, computed, inject, DestroyRef, NgZone, ElementRef } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, Observable } from 'rxjs';
import { UIDesignCanvasService } from './ui-design-canvas.service';
import { UIDesignViewportService } from './ui-design-viewport.service';

export interface CanvasEventData {
  clientX: number;
  clientY: number;
  button?: number;
  deltaY?: number;
  ctrlKey?: boolean;
  metaKey?: boolean;
  shiftKey?: boolean;
}

export interface CanvasState {
  isInitialized: boolean;
  isDragging: boolean;
  isZooming: boolean;
  lastInteraction: number;
}

@Injectable({
  providedIn: 'root'
})
export class CodeWindowCanvasOperationsService {
  private destroyRef = inject(DestroyRef);
  private ngZone = inject(NgZone);
  private uiDesignCanvasService = inject(UIDesignCanvasService);
  private uiDesignViewportService = inject(UIDesignViewportService);

  readonly isCanvasInitialized = signal<boolean>(false);
  readonly isCanvasDragging = signal<boolean>(false);
  readonly isCanvasZooming = signal<boolean>(false);
  readonly canvasInteractionCount = signal<number>(0);
  readonly lastCanvasInteraction = signal<number>(0);

  private canvasState$ = new BehaviorSubject<CanvasState>({
    isInitialized: false,
    isDragging: false,
    isZooming: false,
    lastInteraction: 0
  });
  private canvasEventLog$ = new BehaviorSubject<CanvasEventData[]>([]);

  private canvasElement: ElementRef | null = null;
  private canvasContentElement: ElementRef | null = null;
  private wheelEventListener: ((event: WheelEvent) => void) | null = null;
  private resizeTimeout: number | null = null;
  private canvasResizeObserver: ResizeObserver | null = null;

  readonly canvasState = this.canvasState$.asObservable();
  readonly canvasEventLog = this.canvasEventLog$.asObservable();

  readonly canInteractWithCanvas = computed(() => 
    this.isCanvasInitialized() && !this.isCanvasZooming()
  );
  readonly canvasHealth = computed(() => {
    if (!this.isCanvasInitialized()) return 'uninitialized';
    if (this.isCanvasDragging()) return 'dragging';
    if (this.isCanvasZooming()) return 'zooming';
    return 'ready';
  });

  constructor() {
    this.initializeCanvasOperations();
  }

  handleCanvasMouseDown(event: MouseEvent): void {
    if (event.button === 0) {
      this.ngZone.run(() => {
        this.isCanvasDragging.set(true);
        this.canvasInteractionCount.update(count => count + 1);
        this.lastCanvasInteraction.set(Date.now());
        
        this.uiDesignCanvasService.startDragging(event.clientX, event.clientY);
        
        this.logCanvasEvent({
          clientX: event.clientX,
          clientY: event.clientY,
          button: event.button
        });
      });
    }
  }

  handleCanvasMouseMove(event: MouseEvent): void {
    const viewport = this.uiDesignCanvasService.getViewport();

    if (viewport.isDragging) {
      this.ngZone.run(() => {
        const deltaX = event.clientX - viewport.lastMouseX;
        const deltaY = event.clientY - viewport.lastMouseY;

        this.uiDesignCanvasService.panCanvas(deltaX, deltaY);
        this.uiDesignCanvasService.updateViewport({
          lastMouseX: event.clientX,
          lastMouseY: event.clientY,
        });

        this.logCanvasEvent({
          clientX: event.clientX,
          clientY: event.clientY
        });
      });
    }
  }

  handleCanvasMouseUp(_event: MouseEvent): void {
    this.ngZone.run(() => {
      this.isCanvasDragging.set(false);
      this.uiDesignCanvasService.stopDragging();
      
      this.updateCanvasState({
        isDragging: false,
        lastInteraction: Date.now()
      });
    });
  }

  handleCanvasWheel(event: WheelEvent, canvasElement: ElementRef): void {
    const rect = canvasElement.nativeElement.getBoundingClientRect();
    if (!rect) return;

    const isOverCanvas = event.clientX >= rect.left &&
                        event.clientX <= rect.right &&
                        event.clientY >= rect.top &&
                        event.clientY <= rect.bottom;

    if (!isOverCanvas) return;

    event.preventDefault();

    const centerPoint = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top,
    };

    this.ngZone.run(() => {
      this.isCanvasZooming.set(true);
      
      if (event.deltaY < 0) {
        this.uiDesignCanvasService.zoomIn(centerPoint);
      } else {
        this.uiDesignCanvasService.zoomOut(centerPoint);
      }

      this.logCanvasEvent({
        clientX: event.clientX,
        clientY: event.clientY,
        deltaY: event.deltaY
      });

      setTimeout(() => {
        this.isCanvasZooming.set(false);
      }, 100);
    });
  }

  setupCanvasWheelListener(canvasContentElement: ElementRef): void {
    if (canvasContentElement?.nativeElement) {
      this.canvasContentElement = canvasContentElement;

      this.wheelEventListener = (event: WheelEvent) => {
        this.handleCanvasWheel(event, this.canvasElement!);
      };

      this.ngZone.runOutsideAngular(() => {
        canvasContentElement.nativeElement.addEventListener('wheel', this.wheelEventListener, {
          passive: false,
          capture: false
        });
      });
    }
  }

  setupAutoCanvasCentering(canvasElement: ElementRef): void {
    this.canvasElement = canvasElement;
    
    this.updateCanvasContainerSize();

    setTimeout(() => {
      this.centerCanvasOnNodes();
    }, 100);

    if (canvasElement?.nativeElement && typeof ResizeObserver !== 'undefined') {
      const resizeObserver = new ResizeObserver(entries => {
        for (const _entry of entries) {
          if (this.resizeTimeout) {
            clearTimeout(this.resizeTimeout);
          }
          this.resizeTimeout = window.setTimeout(() => {
            this.updateCanvasContainerSize();
            this.centerCanvasOnNodes();
          }, 150);
        }
      });

      resizeObserver.observe(canvasElement.nativeElement);
      this.canvasResizeObserver = resizeObserver;
    }

    this.isCanvasInitialized.set(true);
    this.updateCanvasState({
      isInitialized: true,
      lastInteraction: Date.now()
    });
  }

  zoomInCanvas(): void {
    this.uiDesignCanvasService.zoomIn();
    this.canvasInteractionCount.update(count => count + 1);
  }

  zoomOutCanvas(): void {
    this.uiDesignCanvasService.zoomOut();
    this.canvasInteractionCount.update(count => count + 1);
  }

  resetCanvasView(): void {
    this.updateCanvasContainerSize();
    this.uiDesignCanvasService.resetViewport();

    setTimeout(() => {
      this.centerCanvasOnNodes();
    }, 50);
  }

  fitCanvasToView(): void {
    this.uiDesignViewportService.fitContentToView();
  }

  centerCanvasOnNodes(): void {
    this.uiDesignViewportService.centerViewOnNodes();
  }

  centerCanvasOnNodesWithViewport(viewport: { x: number; y: number; zoom: number }): void {
    this.uiDesignCanvasService.updateViewport({
      x: viewport.x,
      y: viewport.y,
      zoom: viewport.zoom,
    });
  }

  getCanvasTransformStyle(): string {
    return this.uiDesignCanvasService.getTransformStyle();
  }

  getCanvasZoomPercentage(): number {
    return this.uiDesignCanvasService.getZoomPercentage();
  }

  isCanvasAtMinZoom(): boolean {
    return this.uiDesignCanvasService.isAtMinZoom();
  }

  isCanvasAtMaxZoom(): boolean {
    return this.uiDesignCanvasService.isAtMaxZoom();
  }

  private updateCanvasContainerSize(): void {
  }

  private logCanvasEvent(eventData: CanvasEventData): void {
    const currentLog = this.canvasEventLog$.value;
    const newLog = [...currentLog, eventData].slice(-50);
    this.canvasEventLog$.next(newLog);
  }

  private updateCanvasState(updates: Partial<CanvasState>): void {
    const currentState = this.canvasState$.value;
    this.canvasState$.next({
      ...currentState,
      ...updates
    });
  }

  private initializeCanvasOperations(): void {
    this.destroyRef.onDestroy(() => {
      this.cleanupCanvasOperations();
    });
  }

  private cleanupCanvasOperations(): void {
    if (this.wheelEventListener && this.canvasContentElement?.nativeElement) {
      this.canvasContentElement.nativeElement.removeEventListener('wheel', this.wheelEventListener);
      this.wheelEventListener = null;
    }

    if (this.canvasResizeObserver) {
      this.canvasResizeObserver.disconnect();
      this.canvasResizeObserver = null;
    }

    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout);
      this.resizeTimeout = null;
    }

    this.isCanvasInitialized.set(false);
    this.isCanvasDragging.set(false);
    this.isCanvasZooming.set(false);
  }

  getCurrentCanvasState(): {
    signals: {
      isInitialized: boolean;
      isDragging: boolean;
      isZooming: boolean;
      interactionCount: number;
      canInteract: boolean;
    };
    health: string;
    eventLogCount: number;
  } {
    return {
      signals: {
        isInitialized: this.isCanvasInitialized(),
        isDragging: this.isCanvasDragging(),
        isZooming: this.isCanvasZooming(),
        interactionCount: this.canvasInteractionCount(),
        canInteract: this.canInteractWithCanvas(),
      },
      health: this.canvasHealth(),
      eventLogCount: this.canvasEventLog$.value.length
    };
  }
}
