<!doctype html>
<html lang="en" data-theme="light">
  <head>
    <meta charset="utf-8" />
    <title>Console</title>
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="favicon.ico" />
    <link rel="icon" type="image/png" sizes="32x32" href="favicon.ico" />
    <link rel="icon" type="image/png" sizes="16x16" href="favicon.ico" />
    <meta name="theme-color" content="#1F2937" />
    <meta name="application-name" content="Console" />
    <!-- Mulish font import -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Mulish:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    
    <!-- Environment configuration - DevOps provided for production, local for development -->
    <script>
      // Check if running on localhost (development)
      const isLocalDevelopment = window.location.hostname === 'localhost' || 
                                window.location.hostname === '127.0.0.1';
      
      // Helper function to load a script with absolute path resolution
      function loadScript(src) {
        const script = document.createElement('script');
        
        // Resolve the correct base path for the script
        let scriptPath;
        if (isLocalDevelopment) {
          // For local development, use relative path as is
          scriptPath = src;
        } else {
          // For production, determine the correct base path
          const currentPath = window.location.pathname;
          let basePath = '';
          
          // Determine which app we're in based on the current URL
          if (currentPath.startsWith('/console/')) {
            basePath = '/console/';
          } else if (currentPath.startsWith('/launchpad/')) {
            basePath = '/launchpad/';
          } else if (currentPath.startsWith('/experience/')) {
            basePath = '/experience/';
          } else if (currentPath.startsWith('/product/')) {
            basePath = '/product/';
          } else {
            // Default to console if we can't determine
            basePath = '/console/';
          }
          
          scriptPath = basePath + src;
        }
        
        script.src = scriptPath;
        script.onerror = function() {
          console.error(`❌ Script failed to load: ${scriptPath}`);
          if (!isLocalDevelopment) {
            console.error('❌ Production environment detected but assets/env.js not found!');
            console.error('❌ Please ensure DevOps pipeline provides environment variables');
          }
        };
        document.head.appendChild(script);
      }

      // Local development (localhost)
      if (isLocalDevelopment) {
        // Uses your existing localEnvSetup.js
        loadScript('localEnvSetup.js');
      } else {
        // Production - uses DevOps-provided env.js
        loadScript('assets/env.js');
      }
    </script>
    
    <!-- Since we're having issues with ReactFlow, let's remove it and focus on our custom implementation -->
  </head>
  <body>
    <app-root></app-root>
  </body>
</html>
