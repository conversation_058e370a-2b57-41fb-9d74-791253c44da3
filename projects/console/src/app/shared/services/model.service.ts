import { Injectable } from '@angular/core';
import { Observable, map } from 'rxjs';
import { environment } from '../../../environments/environment';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Model, GetAllModelResponse, GetModelResponse } from '../models/card.model';

@Injectable({
  providedIn: 'root',
})
export class ModelService {
  private apiServiceUrl  = environment.consoleApi;
  private baseUrl = environment.baseUrl;

  private readonly HttpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
  };

  getAllModelList(): Observable<Model[]> {
    const url = `${this.baseUrl}/models`
    return this.http.get<GetAllModelResponse>(url, this.HttpOptions).pipe(
      map((response) => {
        return response.models;
      })
    );
  }

  getGenerativeModels(): Observable<Model[]> {
    const url = `${this.baseUrl}/models?modelType=Generative`;
    return this.http.get<GetAllModelResponse>(url, this.HttpOptions).pipe(
      map((response) => {
        return response.models;
      })
    );
  }

  /**
   * Fetch models by model type and AI engine, expecting the new array-of-objects format.
   * @param modelType The type of the model (e.g., 'Generative', 'Embedding')
   * @param aiEngine The AI engine (e.g., 'AzureOpenAI', 'AmazonBedrock')
   */
  getModelsByTypeAndEngine(modelType: string, aiEngine: string): Observable<{ type: string; models: { id: string; name: string }[] }[]> {
    const url = `${this.baseUrl}/models/list?modelType=${encodeURIComponent(modelType)}&aiEngine=${encodeURIComponent(aiEngine)}`;
    return this.http.get<{ type: string; models: { id: string; name: string }[] }[]>(url, this.HttpOptions);
  }

  public getDropdownOptions(refKey: string, reverseLabel: boolean = false, rawArray: boolean = false) {
    const url = `${this.apiServiceUrl }/ava/force/refdata?ref_key=${encodeURIComponent(refKey)}`;
    return this.http.get(url, this.HttpOptions).pipe(
      map((res: any) => {
        const parsed = JSON.parse(res.value);
        if (rawArray && Array.isArray(parsed)) {
          return parsed;
        }
        const optionsArray = Object.keys(parsed).map(key => {
          return reverseLabel
            ? { value: parsed[key], label: key }
            : { value: key, label: parsed[key] };
        });
        return optionsArray;
      })
    );
  }

  saveModel(payload: Model): Observable<any> {
    const url = `${this.baseUrl}/models`
    return this.http.post(url, payload, this.HttpOptions);
  }

  getOneModeById(id: string | number) {
    const url = `${this.baseUrl}/models`
    let param = new HttpParams();
    param = param.append('modelId', id);
    return this.http.get<GetModelResponse>(url, { params: param, headers: this.HttpOptions.headers }).pipe(
      map((response) => {
        return response.model;
      })
    );
  }
  constructor(private http: HttpClient) { }
}
