import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from 'projects/console/src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class RealmService {
  private apiAuthUrl = environment.consoleApiAuthUrl;

  private http = inject(HttpClient); 

  getAllRealm() {
    const url = `${this.apiAuthUrl}/realms`;
    return this.http.get(url);
  }

  createRealm(name: string, teamId: string) {
    const url = `${this.apiAuthUrl}/realm?realmName=${name}&teamId=${teamId}`;
    return this.http.post(url, null);
  }

  updateRealm(name: string, teamId: string, realmId: string) {
    const url = `${this.apiAuthUrl}/realm?realmName=${name}&teamId=${teamId}&realmId=${realmId}`;
    return this.http.put(url, null);
  }

  deleteRealm(id: number) {
    const url = `${this.apiAuthUrl}/realm?realmId=${id}`;
    return this.http.delete(url);
  }
}