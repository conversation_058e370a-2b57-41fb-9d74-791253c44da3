import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { filter } from 'rxjs/operators';

export interface Breadcrumb {
  label: string;
  url: string;
  active: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class BreadcrumbService {
  private breadcrumbsSubject = new BehaviorSubject<Breadcrumb[]>([]);
  public breadcrumbs$: Observable<Breadcrumb[]> = this.breadcrumbsSubject.asObservable();

  private routeMap: { [key: string]: string } = {
    '/dashboard': 'Dashboard',
    '/build': 'Build',
    '/build/agents': 'Agents',
    '/build/agents/create': 'Create Agent',
    '/build/agents/individual': 'Build Individual',
    '/build/agents/collaborative': 'Build Collaborative',
    '/build/workflows': 'Workflows',
    '/libraries': 'Libraries',
    '/library1': 'Library 1',
    '/library2': 'Library 2',
    '/manage': 'User Management',
    '/manage1': 'Management 1',
    '/manage2': 'Management 2',
    '/analytics': 'Analytics',
    '/analytics1': 'Analytics 1',
    '/analytics2': 'Analytics 2'
  };

  constructor(private router: Router, private activatedRoute: ActivatedRoute) {
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: any) => {
      this.updateBreadcrumbs(event.url);
    });
  }

  private updateBreadcrumbs(url: string): void {
    const urlSegments = url.split('/').filter(segment => segment);
    const breadcrumbs: Breadcrumb[] = [];

    let currentUrl = '';

    // Add home/dashboard as first breadcrumb if not on dashboard
    if (url !== '/dashboard' && url !== '/') {
      // Add each segment to the breadcrumbs
      urlSegments.forEach((segment, index) => {
        segment=segment.split('?')[0];
        currentUrl += `/${segment}`;
        const label = this.routeMap[currentUrl] || this.capitalizeFirstLetter(segment);

        breadcrumbs.push({
          label,
          url: currentUrl,
          active: index === urlSegments.length - 1
        });
      });
    }

    this.breadcrumbsSubject.next(breadcrumbs);
  }

  private capitalizeFirstLetter(string: string): string {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }
}
