import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'projects/console/src/environments/environment';


/**
 * Example service demonstrating how to use API_BASE_URL from Docker environment
 * 
 * This service shows different patterns for using the dynamic API base URL:
 * 1. Using environment.baseUrl directly
 * 2. Using the helper function environment.getApiUrl()
 * 3. Accessing window.__env directly for runtime values
 */
@Injectable({
  providedIn: 'root'
})
export class ApiExampleService {
  
  private headers = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    })
  };

  constructor(private http: HttpClient) {}

  /**
   * Method 1: Using environment.baseUrl
   * This gets the API base URL from the environment configuration
   */
  getUsers(): Observable<any> {
    const url = `${environment.baseUrl}/api/users`;
    return this.http.get(url, this.headers);
  }

  /**
   * Method 2: Using the helper function
   * This is the recommended approach for consistency
   */
  getUserById(id: string): Observable<any> {
    const baseUrl = (window as any).__env?.API_BASE_URL || environment.baseUrl;
    const url = `${baseUrl}/api/users/${id}`;
    return this.http.get(url, this.headers);
  }

  /**
   * Method 3: Direct access to window.__env for runtime values
   * This allows you to get the most up-to-date environment values
   */
  createUser(userData: any): Observable<any> {
    const baseUrl = (window as any).__env?.API_BASE_URL || environment.baseUrl;
    const url = `${baseUrl}/api/users`;
    return this.http.post(url, userData, this.headers);
  }

  /**
   * Method 4: Building complex API URLs
   * This shows how to handle different API versions or endpoints
   */
  getApiEndpoint(endpoint: string, version: string = 'v1'): string {
    const baseUrl = environment.baseUrl;
    return `${baseUrl}/${version}/api/${endpoint}`;
  }

  /**
   * Helper method to get current environment info
   * Useful for debugging and logging
   */
  getEnvironmentInfo(): any {
    return {
      baseUrl: environment.baseUrl,
      runtimebaseUrl: (window as any).__env?.API_BASE_URL,
      isProduction: environment.production,
      windowEnv: (window as any).__env
    };
  }
} 