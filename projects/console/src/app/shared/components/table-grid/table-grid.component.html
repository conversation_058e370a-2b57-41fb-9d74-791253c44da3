<div class="table__grid--container" [style.height.px]="height()">
  <div class="table__grid--wrapper" role="table">
    <!-- Header Row -->
    <div class="table__header" role="rowgroup">
      @for (column of columnDefs(); track column; let i = $index) {
        <div
          class="header-cell"
          role="columnheader"
          tabindex="0"
          [style.flex]="getColumnFlex(i)"
          [style.min-width.px]="getColumnMinWidth(i)"
          [style.max-width.px]="getColumnMaxWidth(i)"
          [style.height.px]="headerHeight()"
        >
          <div class="cell-content">
            <span class="table__header--text">{{ column.header }}</span>
          </div>
        </div>
      }
    </div>

    <!-- Body Rows -->
    <div class="table__body" role="rowgroup">
      <!-- Skeleton Loading Rows -->
      @if (loading()) {
        @for (skeletonIndex of skeletonRowsArray(); track skeletonIndex) {
          <div class="table__row skeleton-row" role="row">
            @for (column of columnDefs(); track column; let i = $index) {
              <div
                class="table__cell data-cell skeleton-cell"
                role="gridcell"
                [style.flex]="getColumnFlex(i)"
                [style.min-width.px]="getColumnMinWidth(i)"
                [style.max-width.px]="getColumnMaxWidth(i)"
              >
                <div class="cell-content">
                  <div class="skeleton-content"></div>
                </div>
              </div>
            }
          </div>
        }
      }

      <!-- Actual Data Rows -->
      @if (!loading()) {
        @for (row of paginatedData(); track row; let rowIndex = $index) {
          <div class="table__row" role="row" [class.hoverable]="hoverable()">
            @for (column of columnDefs(); track column; let i = $index) {
              <div
                class="table__cell data-cell"
                role="gridcell"
                [style.flex]="getColumnFlex(i)"
                [style.min-width.px]="getColumnMinWidth(i)"
                [style.max-width.px]="getColumnMaxWidth(i)"
              >
                <div class="cell-content">
                  @if (column.cellRenderer) {
                    <div
                      aweCellRenderer
                      [cellRenderer]="column.cellRenderer"
                      [value]="row[column.field]"
                      [rowData]="row"
                      [colDef]="column"
                      [rowIndex]="rowIndex"
                      class="cell-renderer-container"
                    ></div>
                  } @else {
                    <span class="table__body--text">{{
                      row[column.field]
                    }}</span>
                  }
                </div>
              </div>
            }
          </div>
        }
      }
    </div>

    <!-- Empty State -->
    @if (!loading() && !rowData().length) {
      <div class="table__row empty-state" role="row">
        <div class="table__cell data-cell empty-cell" role="gridcell">
          <div class="empty-message" role="status">
            {{ emptyStateMessage() }}
          </div>
        </div>
      </div>
    }
  </div>
</div>

<!-- Pagination -->
@if (pagination() && totalRowData() > 0) {
  <div class="pagination__container">
    <app-pagination
      [totalItems]="totalRowData()"
      [currentPage]="currentPage()"
      [itemsPerPage]="paginationPageSize()"
      [visiblePageCount]="visiblePageCount()"
      (pageChange)="onPageChange($event)"
    >
    </app-pagination>
  </div>
}
