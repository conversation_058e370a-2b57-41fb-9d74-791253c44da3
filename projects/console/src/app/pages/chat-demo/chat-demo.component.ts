import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChatInterfaceComponent, ChatMessage } from '@shared/index';

@Component({
  selector: 'app-chat-demo',
  standalone: true,
  imports: [CommonModule, ChatInterfaceComponent],
  templateUrl: './chat-demo.component.html',
  styleUrls: ['./chat-demo.component.scss'],
})
export class ChatDemoComponent {
  // Chat interface properties
  placeholder: string = 'Ask here';
  isLoading: boolean = false;

  // Pre-defined chat messages for demonstration
  chatMessages: ChatMessage[] = [
    {
      from: 'ai',
      text: 'Hi Akash, this is the tool testing',
    },
    {
      from: 'user',
      text: 'Test this input',
    },
    {
      from: 'ai',
      text: 'Here is the output',
    },
  ];

  constructor() {}

  /**
   * Handle sending a message
   */
  onMessageSent(message: string): void {
    // Show loading state
    this.isLoading = true;

    // Simulate API response delay
    setTimeout(() => {
      // Add AI response
      this.chatMessages = [
        ...this.chatMessages,
        {
          from: 'ai',
          text: `Received your message: "${message}". This is a simulated response.`,
        },
      ];

      // Reset loading state
      this.isLoading = false;
    }, 1500);
  }

  /**
   * Handle attachment click
   */
  onAttachmentClicked(): void {
    alert('Attachment button clicked - you can implement file selection here');
  }

  /**
   * Reset the chat
   */
  resetChat(): void {
    this.chatMessages = [
      {
        from: 'ai',
        text: 'Hi Akash, this is the tool testing',
      },
    ];
  }
}
