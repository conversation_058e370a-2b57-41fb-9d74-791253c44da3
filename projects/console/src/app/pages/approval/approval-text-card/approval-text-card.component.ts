import { Component, Input } from '@angular/core';
import { TxtCardComponent, CardContentComponent, IconComponent } from "@ava/play-comp-library";

@Component({
  selector: 'app-approval-txt-card',
  imports: [TxtCardComponent, CardContentComponent, IconComponent],
  templateUrl: './approval-text-card.component.html',
  styleUrl: './approval-text-card.component.scss'
})
export class ApprovalTxtCardComponent {
  @Input() title = ""
  @Input() value = 0
  @Input() subtitle = ""
  @Input() iconName = ""

}