::ng-deep #dashboard-txt-card{
    .dashboard-txt-card-1 {
    .ava-text-card-wrapper {
        .ava-default-card-container {
            .ava-default-card.default-card.card {
                border: none;
                box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
                height: 300px;
                width: 420px;
                padding: 24px;
                background: linear-gradient(to left, #ffffff, #f0f5ff);

                ava-card-content {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    height: 100%;

                    .title-wrapper {
                        display: flex;
                        align-items: center;
                        column-gap: 20px;

                        div {
                            margin: 0px;
                            font-weight: 900;
                            font-style: bold;
                            font-size: 20px;
                            color: #3B3F46;
                        }

                    }

                    .value-wrapper {
                        display: flex;
                        flex-direction: column;
                        row-gap: 10px;

                        h1 {
                            margin: 0px;
                            font-weight: 500;
                            font-style: Medium;
                            font-size: 48px;
                            color: #3B3F46;
                        }

                        p {
                            margin: 0px;
                            font-weight: 400;
                            font-style: Regular;
                            font-size: 16px;
                            color: #3B3F46;
                        }
                    }
                }

            }
        }
    }
}
}
