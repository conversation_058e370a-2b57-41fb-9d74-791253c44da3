<div class="approval-right-screen">
    <div>
        <!-- Loading Overlay -->
        @if(isloading){
        <div class="loading-overlay">
            <div class="">
                <ava-cubical-loading background="blue"></ava-cubical-loading>
            </div>
        </div>
        }
        <div class="approval-title-filter">
        <app-approval-txt-card [iconName]="'hourglass'" [title]="labels.totalApprovals" [value]="totalApprovals"
            [subtitle]="currentTab + ' ' + labels.whichAreRequestedForApproval"></app-approval-txt-card>
        <app-approval-txt-card [iconName]="'shield-alert'" [title]="labels.totalApprovedApprovals"
            [value]="totalApprovedApprovals" [subtitle]="currentTab + ' ' + labels.whichAreApproved"></app-approval-txt-card>
        <app-approval-txt-card [iconName]="'hourglass'" [title]="labels.totalPendingApprovals" [value]="totalPendingApprovals"
            [subtitle]="labels.all + ' ' + currentTab + ' ' + labels.awaitingApproval"></app-approval-txt-card>
    </div>
    
<div class="filter-section">
    <div class="search-bars">
        <div style="font-size: 1.5rem; font-weight: bold; color: black;">
            {{currentTab}} Approvals
        </div>
        <div class="approval-card-header">
            All - {{totalRecords}} {{currentTab}}
        </div>  
    </div>
    <div class="textbox section">
        <div>
            <form [formGroup]="searchForm">
                <ava-textbox [placeholder]="labels.searchPlaceholder" formControlName="search">
                    <ava-icon slot="icon-start" iconName="search" [iconSize]="16" iconColor="var(--color-brand-primary)"></ava-icon>
                </ava-textbox>
            </form>
        </div>
    </div>
</div>
    
    <div class="approval-card-section">
        @if(totalRecords > 0){      
            @for (item of consoleApproval.contents; track $index){
            <div class="approval-card-wrapper">
                <ava-approval-card height="300">
                    <div (click)="onCardClick($index)">
                        <ava-card-header>
                            <div class="header">
                                <h2>{{item.session1.title}}</h2>
                                <ava-tag label="{{currentTab}}" color="info" size="sm"></ava-tag>
                            </div>
                        </ava-card-header>
                    </div>

                    <div (click)="onCardClick($index)">
                        <ava-card-content>
                            <div class="a-content">
                                <div class="box tag-wrapper">
                                    <ava-tag label="{{item.session5.org}}" size="sm"></ava-tag>
                                    <ava-tag label="{{item.session5.domain}}" size="sm"></ava-tag>
                                    <ava-tag label="{{item.session5.project}}" size="sm"></ava-tag>
                                    <ava-tag label="{{item.session5.team}}" size="sm"></ava-tag>
                                </div>
                                <div class="box info-wrapper">
                                    <div class="f">
                                        <ava-icon iconSize="13" iconName="user"></ava-icon>
                                        <span>{{item.session3[0].label}}</span>
                                    </div>
                                    <div class="ml-auto s">
                                        <ava-icon iconSize="20" iconName="calendar-days"></ava-icon>
                                        <span>{{item.session3[1].label}}</span>
                                    </div>
                                </div>
                            </div>
                        </ava-card-content>
                    </div>
            
                    <ava-card-footer>
                        <div class="footer-content">
                            <div class="footer-left" (click)="onCardClick($index)">
                                <span class="ex">Execution Status</span>
                                <div>
                                    <ava-icon iconSize="20" iconName="circle-check-big"></ava-icon>
                                    <span>{{item?.session4.status}}</span>
                                </div>
                            </div>
                            <div class="footer-right">
                                <ava-button label="Test" (userClick)="handleTesting($index)" variant="secondary" size="medium"
                                    state="default" iconName="play" iconPosition="left"></ava-button>
                                <ava-button label="Sendback" (userClick)="rejectApproval($index)" variant="secondary" size="medium"
                                    state="default" iconName="move-left" iconPosition="left"></ava-button>
                                <ava-button label="Approve" (userClick)="approveApproval($index)" variant="primary" size="medium"
                                    state="default" iconName="Check" iconPosition="left"></ava-button>
                            </div>
                        </div>
                    </ava-card-footer>
                </ava-approval-card>
            </div>
            }
        }
        @else{
            <div class="no-pending-message">
                All {{currentTab}} have been successfully approved. No pending actions.
            </div>
        }
    </div>

    <div>
        <app-page-footer
        [totalItems]="totalRecords"
        [currentPage]="currentWorkflowsPage"
        [itemsPerPage]="pageSize"
        (pageChange)="loadMoreWorkflows($event)"
      ></app-page-footer>
    </div>
</div>

