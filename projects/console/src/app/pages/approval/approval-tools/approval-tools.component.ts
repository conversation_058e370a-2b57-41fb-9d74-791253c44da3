import { CommonModule, formatDate } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import {
  ApprovalCardComponent,
  IconComponent,
  AvaTextboxComponent,
  AvaTagComponent,
  DropdownOption,
  ButtonComponent,
  DialogService,
  CubicalLoadingComponent,
  DialogButton,
} from '@ava/play-comp-library';
import approvalText from '../constants/approval.json';
import { SharedApiServiceService } from '../../../shared/services/shared-api-service.service';
import { ApprovalService } from '../../../shared/services/approval.service';
import { debounceTime, distinctUntilChanged, startWith } from 'rxjs';
import { ToolsService } from '@shared/services/tools.service';
import { DrawerService } from '../../../shared/services/drawer/drawer.service';
import { AgentsPreviewPanelComponent } from './agents-preview-panel/agents-preview-panel.component';
import { ApprovalTxtCardComponent } from '../approval-text-card/approval-text-card.component';
import { DebouncedSearchService } from '@shared/services/debounced-search.service';
import { PageFooterComponent } from '@shared/index';

type RequestStatus = 'approved' | 'rejected' | 'review';

@Component({
  selector: 'app-approval-tools',
  imports: [
    CommonModule,
    RouterModule,
    ApprovalCardComponent,
    IconComponent,
    AvaTextboxComponent,
    ReactiveFormsModule,
    AvaTagComponent,
    ButtonComponent,
    ApprovalTxtCardComponent,
    CubicalLoadingComponent,
    PageFooterComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './approval-tools.component.html',
  styleUrls: ['./approval-tools.component.scss'],
})
export class ApprovalToolsComponent implements OnInit {
  appLabels = approvalText.labels;

  public totalApprovedApprovals: number = 20;
  public totalPendingApprovals: number = 15;
  public totalApprovals: number = 60;
  public isBasicCollapsed: boolean = false;
  public quickActionsExpanded: boolean = true;
  public consoleApproval: any = {};
  public options: DropdownOption[] = [];
  public basicSidebarItems: any[] = [];
  public quickActions: any[] = [];
  public toolReviews: any[] = [];
  public filteredToolReviews: any[] = [];
  public workflowReviews: any[] = [];
  public agentsReviews: any[] = [];
  public currentToolsPage = 1;
  public currentAgentsPage = 1;
  public currentWorkflowsPage = 1;
  public pageSize = 10;
  public totalRecords = 0;
  public isDeleted = false;
  public currentTab = 'Tools';
  public showToolApprovalPopup = false;
  public showInfoPopup = false;
  public showErrorPopup = false;
  public infoMessage = '';
  public selectedIndex = 0;
  public showFeedbackPopup = false;
  public searchForm!: FormGroup;
  public labels: any = approvalText.labels;
  public approvedAgentId: number | null = null;
  public previewData: any = null;
  public selectedToolId: string = '';
  public feedbackMessage: string = '';
  public isloading: boolean = true;

  constructor(
    private router: Router,
    private apiService: SharedApiServiceService,
    private approvalService: ApprovalService,
    private fb: FormBuilder,
    private toolsService: ToolsService,
    private drawerService: DrawerService,
    private dialogService: DialogService,
    private debounceService: DebouncedSearchService,
  ) {
    this.labels = approvalText.labels;
    this.options = [
      { name: this.labels.electronics, value: 'electronics' },
      { name: this.labels.clothing, value: 'clothing' },
      { name: this.labels.books, value: 'books' },
    ];
    this.basicSidebarItems = [
      {
        id: '1',
        icon: 'hammer',
        text: this.labels.agents,
        route: '',
        active: true,
      },
      { id: '2', icon: 'circle-check', text: this.labels.workflows, route: '' },
      { id: '3', icon: 'bot', text: this.labels.tools, route: '' },
    ];
    this.quickActions = [
      {
        icon: 'awe_agents',
        label: this.labels.agents,
        route: '',
      },
      {
        icon: 'awe_workflows',
        label: this.labels.workflows,
        route: '',
      },
      {
        icon: 'awe_tools',
        label: this.labels.tools,
        route: '',
      },
    ];
    this.searchForm = this.fb.group({
      search: [''],
    });
  }

  ngOnInit(): void {
    this.searchList();
    this.loadToolReviews();
    this.loadToolMetrics();
  }

  public loadToolMetrics() {
    this.isloading = true;
    this.approvalService.getToolsMetrics().subscribe({
      next: (response) => {
        console.log('Tools metrics', response?.userToolsMetrics);
        const metrics = response?.userToolsMetrics;
        this.totalApprovals = metrics?.draftedUserToolsCount;
        this.totalPendingApprovals = metrics?.reviewUserToolsCount;
        this.totalApprovedApprovals = metrics?.approvedUserToolsCount;
        this.isloading = false;
      },
    });
  }

  public searchList() {
    this.searchForm
      .get('search')!
      .valueChanges.pipe(
        startWith(''),
        debounceTime(300),
        distinctUntilChanged(),
      )
      .subscribe((searchText: string) => {
        const trimmed = searchText.trim();
        if (trimmed) {
          this.isloading = true;
          this.debounceService.triggerSearch(trimmed, 'approval', 'tools');
        } else {
          this.loadToolReviews();
        }
      });

    this.debounceService.searchResults$.subscribe({
      next: (results: any) => {
        this.isloading = false;
        this.toolReviews = results?.toolReviewDetails;
        this.filteredToolReviews = this.toolReviews;
        this.totalRecords = results?.totalNoOfRecords;
        this.updateConsoleApproval(this.toolReviews, 'tool');
      },
      error: () => {
        this.isloading = false;
      },
    });
  }

  public applyFilter(text: string) {
    const lower = text;

    if (!text) {
      this.updateConsoleApproval(this.toolReviews, 'tool');
      return;
    }

    this.filteredToolReviews = this.toolReviews.filter((item) =>
      item.toolName?.toLowerCase().includes(lower),
    );

    this.updateConsoleApproval(this.filteredToolReviews, 'tool');
  }

  public onSelectionChange(data: any) {
    console.log('Selection changed:', data);
  }

  public uClick(i: any) {
    console.log('log' + i);
  }

  public toggleQuickActions(): void {
    this.quickActionsExpanded = !this.quickActionsExpanded;
  }

  public onBasicCollapseToggle(isCollapsed: boolean): void {
    this.isBasicCollapsed = isCollapsed;
    console.log('Basic sidebar collapsed:', isCollapsed);
  }

  public onBasicItemClick(item: any): void {
    this.basicSidebarItems.forEach((i) => (i.active = false));
    item.active = true;
    console.log(item);
  }

  public toRequestStatus(value: string | null | undefined): RequestStatus {
    return value === 'approved' || value === 'rejected' || value === 'review'
      ? value
      : 'review';
  }

  public loadToolReviews() {
    this.isloading = true;
    this.approvalService
      .getReviewsForTools(this.currentToolsPage, this.pageSize, this.isDeleted)
      .subscribe({
        next: (response) => {
          this.toolReviews = response?.toolReviewDetails;
          this.totalRecords = response?.totalNoOfRecords;
          this.filteredToolReviews = this.toolReviews;
          // console.log('tool reviews ', this.toolReviews);
          // this.totalRecords = this.toolReviews.length;
          this.updateConsoleApproval(this.toolReviews, 'tool');
          this.isloading = false;
        },
        error: (error) => {
          console.error('Error loading tool reviews:', error);
          this.isloading = false;
        },
      });
  }

  public loadMoreTools(page: number) {
    this.currentToolsPage = page;
    this.loadToolReviews();
  }

  public loadReviews(name: string) {
    this.currentTab = name;
    this.loadToolReviews();
  }

  public rejectApproval(idx: any) {
    console.log(idx);
    this.selectedIndex = idx;
    this.showFeedbackDialog();
  }

  public approveApproval(idx: any) {
    console.log(idx);
    this.selectedIndex = idx;
    console.log(this.filteredToolReviews[this.selectedIndex]);
    this.showApprovalDialog();
  }

  public handleApproval() {
    this.handleToolApproval();
  }

  public handleRejection(feedback: any) {
    console.log(this.selectedIndex);
    this.handleToolRejection(feedback);
  }

  private showApprovalDialog(): void {
    this.dialogService
      .confirmation({
        title: this.labels.confirmApproval,
        message: `${this.labels.youAreAboutToApproveThis} Tool. ${this.labels.itWillBeActiveAndAvailableIn} ${this.currentTab} ${this.labels.catalogueForUsersToExecute}`,
        confirmButtonText: this.labels.approve,
        cancelButtonText: 'Cancel',
        confirmButtonVariant: 'danger',
        icon: 'circle-check',
      })
      .then((result) => {
        if (result.confirmed) {
          this.handleApproval();
        }
      });
  }

  private showFeedbackDialog(): void {
    const customButtons: DialogButton[] = [
      { label: 'Cancel', variant: 'secondary', action: 'cancel' },
      { label: 'Send Back', variant: 'primary', action: 'sendback' },
    ];
    this.dialogService
      .feedback({
        title: 'Confirm Send Back',
        message:
          'This Tool will be send back for corrections and modification. Kindly comment what needs to be done.',
        buttons: customButtons,
      })
      .then((result) => {
        if (result.confirmed && result.confirmed === true) {
          this.handleRejection(result.data);
        }
      });
  }

  public handleMetaDataApproval() {
    this.showApprovalDialog();
  }

  public handeMetaDataSendback() {
    this.showFeedbackDialog();
  }

  public onCardClick(index: number): void {
    console.log('Selected card index:', index);
    this.selectedIndex = index;
    const selectedTool = this.filteredToolReviews[this.selectedIndex];
    this.loadPreviewData(selectedTool);
    this.selectedToolId = selectedTool.id;

    this.drawerService.open(AgentsPreviewPanelComponent, {
      previewData: this.previewData,
      closePreview: () => this.drawerService.clear(),
      editTool: () => this.handleEditTool(selectedTool?.id),
      rejectApproval: () => this.handeMetaDataSendback(),
      approveApproval: () => this.handleMetaDataApproval(),
      testApproval: () => this.redirectToToolPlayground(),
    });
    console.log(selectedTool);
  }

  private loadPreviewData(item: any): void {
    this.previewData = {
      type: 'tool',
      title: item.toolName,
      data: { ...item },
      loading: true,
      error: null,
    };
    console.log(this.previewData);
    this.loadToolDetails(item);
  }

  private loadToolDetails(item: any): void {
    if (item.id && this.toolsService.getUserToolDetails) {
      // Extract numeric ID from string like "user-423"
      let toolId: number;

      if (typeof item.id === 'string' && item.id.startsWith('user-')) {
        // Extract number from "user-423" format
        const numericPart = item.id.replace('user-', '');
        toolId = Number(numericPart);
      } else {
        toolId = Number(item.id);
      }

      if (isNaN(toolId)) {
        console.warn('Invalid tool ID:', item.id, 'Using fallback data');
        this.previewData.loading = false;
        return;
      }

      this.toolsService.getUserToolDetails(toolId).subscribe({
        next: (response: any) => {
          console.log('Tool details response:', response);
          let toolDetail, toolConfigs;

          if (response.userToolDetail) {
            toolDetail = response.userToolDetail;
            toolConfigs = toolDetail.toolConfigs || {};
          } else if (response.tools && response.tools[0]) {
            const tool = response.tools[0];
            toolDetail = {
              id: tool.toolId,
              name: tool.toolName,
              description: tool.toolDescription,
              createdBy: tool.createdBy,
              createdAt: tool.createTimestamp,
              modifiedAt: tool.updateTimestamp,
              isDeleted: !tool.isApproved,
            };
            toolConfigs = {
              tool_class_name: [tool.toolClassName],
              tool_class_def: [tool.toolClassDef],
            };
          } else {
            // Fallback
            toolDetail = response;
            toolConfigs = {};
          }

          // Extract functionality/code from various possible fields
          let functionality = 'Tool code not found';
          if (toolConfigs.tool_class_def && toolConfigs.tool_class_def[0]) {
            functionality = toolConfigs.tool_class_def[0];
          } else if (toolDetail.toolClassDef) {
            functionality = toolDetail.toolClassDef;
          } else if (toolDetail.code) {
            functionality = toolDetail.code;
          } else if (toolDetail.definition) {
            functionality = toolDetail.definition;
          }

          this.previewData.data = {
            ...this.previewData.data,
            id: toolDetail.id,
            name: toolDetail.name || item.name,
            description: toolDetail.description || item.description,
            className:
              toolConfigs.tool_class_name?.[0] ||
              toolDetail.toolClassName ||
              'Unknown',
            functionality: functionality,
            isApproved: !toolDetail.isDeleted,
            createdBy: toolDetail.createdBy || 'Unknown',
            createdOn:
              toolDetail.createdAt ||
              toolDetail.createTimestamp ||
              new Date().toISOString(),
            modifiedBy: toolDetail.modifiedBy,
            modifiedAt: toolDetail.modifiedAt || toolDetail.updateTimestamp,
            isDeleted: toolDetail.isDeleted || false,
          };
          this.previewData.loading = false;
        },
        error: (error: any) => {
          console.error('Error loading tool details:', error);
          this.previewData.error = 'Failed to load tool details';
          this.previewData.loading = false;
        },
      });
    } else {
      this.previewData.loading = false;
    }
  }

  public handleEditTool(toolId: number) {
    console.log('Edit tool', toolId);
    // this.drawerService.clear();
    // this.router.navigate(['/libraries/tools/edit', toolId]);
  }

  public handleToolApproval() {
    const toolDetails = this.filteredToolReviews[this.selectedIndex];
    const id = toolDetails.id;
    const toolId = toolDetails.id;
    const status = 'APPROVED';
    const reviewedBy = toolDetails.reviewedBy;

    // Show loading dialog
    this.dialogService.loading({
      title: 'Approving Tool...',
      message: 'Please wait while we approve the tool.',
      showProgress: false,
      showCancelButton: false,
    });

    this.approvalService.approveTool(id, toolId, status, reviewedBy).subscribe({
      next: (response: any) => {
        this.dialogService.close(); // Close loading dialog

        const message =
          response?.message || this.labels.toolSuccessApproveMessage;
        this.dialogService
          .success({
            title: 'Tool Approved',
            message: message,
          })
          .then((result) => {
            if (result.action === 'secondary') {
              // Navigate to edit tool screen
              this.router.navigate(['/libraries/tools/edit', toolId]);
            } else {
              this.loadToolReviews(); // Refresh the list
            }
          });
      },
      error: (error) => {
        this.dialogService.close(); // Close loading dialog

        const errorMessage =
          error?.error?.message || this.labels.defaultErrorMessage;
        this.dialogService
          .error({
            title: 'Approval Failed',
            message: errorMessage,
            showRetryButton: true,
            retryButtonText: 'Retry',
          })
          .then((result) => {
            if (result.action === 'retry') {
              this.handleToolApproval();
            }
          });
      },
    });
  }

  public handleToolRejection(feedback: any) {
    const toolDetails = this.filteredToolReviews[this.selectedIndex];
    const id = toolDetails.id;
    const toolId = toolDetails.id;
    const status = 'REJECTED';
    const reviewedBy = toolDetails.reviewedBy;
    const message = feedback;

    // Show loading dialog
    this.dialogService.loading({
      title: 'Rejecting Tool...',
      message: 'Please wait while we reject the tool.',
      showProgress: false,
      showCancelButton: false,
    });

    this.approvalService
      .rejectTool(id, toolId, status, reviewedBy, message)
      .subscribe({
        next: (response: any) => {
          this.dialogService.close(); // Close loading dialog
          const message =
            response?.message || this.labels.toolSuccessRejectMessage;
          this.dialogService
            .success({
              title: 'Tool Rejected',
              message: message,
            })
            .then(() => {
              this.loadToolReviews(); // Refresh the list
            });
        },
        error: (error) => {
          this.dialogService.close(); // Close loading dialog
          const errorMessage =
            error?.error?.message || this.labels.defaultErrorMessage;
          this.dialogService
            .error({
              title: 'Rejection Failed',
              message: errorMessage,
              showRetryButton: true,
              retryButtonText: 'Retry',
            })
            .then((result) => {
              if (result.action === 'retry') {
                this.handleToolRejection(feedback);
              }
            });
        },
      });
  }

  public handleTesting(index: any) {
    console.log(index);
    this.selectedToolId = this.filteredToolReviews[index].id;
    this.redirectToToolPlayground();
  }

  public redirectToToolPlayground(): void {
    this.drawerService.clear();
    this.router.navigate(['/libraries/tools/execute', this.selectedToolId]);
  }

  public redirectToTool(): void {
    this.router.navigate(['/libraries/tools/edit', this.selectedToolId]);
  }

  public updateConsoleApproval(data: any[], type: string) {
    this.consoleApproval = {
      contents: data?.map((req: any) => {
        const statusIcons: Record<RequestStatus, string> = {
          approved: 'circle-check-big',
          rejected: 'circle-x',
          review: 'clock',
        };
        const statusTexts: Record<RequestStatus, string> = {
          approved: this.labels.approved,
          rejected: this.labels.rejected,
          review: this.labels.review,
        };
        const statusKey = this.toRequestStatus(req?.status);
        const specificId = req.id;
        const title = req.name;

        return {
          id: req.id,
          refId: specificId,
          type: type,
          session1: {
            title: title,
            labels: [
              {
                name: type,
                color: 'success',
                background: 'red',
                type: 'normal',
              },
              {
                name: req.changeRequestType,
                color: req.changeRequestType === 'update' ? 'error' : 'info',
                background: 'red',
                type: 'pill',
              },
            ],
          },
          session2: [
            {
              name: type,
              color: 'default',
              background: 'red',
              type: 'normal',
            },
            {
              name: req.status,
              color: 'default',
              background: 'red',
              type: 'normal',
            },
          ],
          session3: [
            {
              iconName: 'user',
              label: req.createdBy,
            },
            {
              iconName: 'calendar-days',
              label: formatDate(req?.createdAt, 'dd MMM yyyy', 'en-IN'),
            },
          ],
          session4: {
            status: statusTexts[statusKey],
            iconName: statusIcons[statusKey],
          },
          session5: {
            org: req?.teamInfo?.org || 'Individual',
            domain: req?.teamInfo?.domain || 'Digital Ascender',
            project: req?.teamInfo?.project || 'Platform Engineering',
            team: req?.teamInfo?.team || 'Platform Engineering',
          },
        };
      }),
      footer: {},
    };
  }
}
