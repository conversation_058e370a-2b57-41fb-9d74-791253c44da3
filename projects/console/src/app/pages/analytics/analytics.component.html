<div class="analytics-container">
    <div class="analytics-header">
        <h3>Agent Analytics</h3>
    </div>

    <!-- Tab Navigation with Pill Buttons -->
    <div class="analytics-tabs">
        <ava-button label="Individual" [variant]="activeTab === 'usecase' ? 'primary' : 'secondary'" [pill]="true"
            size="large" (userClick)="onTabChange({id: 'usecase', label: 'Individual'})">
        </ava-button>
        <ava-button label="Collaborative" [variant]="activeTab === 'agents' ? 'primary' : 'secondary'" [pill]="true"
            size="large" (userClick)="onTabChange({id: 'agents', label: 'Collaborative'})">
        </ava-button>
    </div>



    <div class="analytics-content">



        <!-- Use Cases Tab Content -->
        <div *ngIf="activeTab === 'usecase'" class="tab-content">

            <!-- Inline Date Filter -->
            <div class="analytics-controls" [formGroup]="vmFG">
                <div class="controls-row">
                    <!-- Left Side - Action Buttons -->
                    <div class="left-buttons">
                        <ava-button label="Langfuse" variant="secondary" size="medium" (userClick)="goToLangfuse()">
                        </ava-button>
                        <ava-button label="ICL Analytics" variant="secondary" size="medium"
                            (userClick)="goToIclAnalytics()">
                        </ava-button>

                        <!-- Download Button -->
                        <ava-button label="Download" iconPosition="left" size="medium" iconName="Download"
                            [iconSize]="20" variant="secondary" class="download-button"
                            (userClick)="onDownloadToggle($event)"
                            (click)="onDownloadToggle($event)">
                        </ava-button>
                        <div *ngIf="showDownloadOptions" class="download-dropdown">
                            <button class="dropdown-item" (click)="onPdfDownload()">
                                <ava-icon iconName="FileText"></ava-icon>
                                <span>Export PDF</span>
                            </button>
                            <button class="dropdown-item" (click)="onDataDownload()">
                                <ava-icon iconName="Database"></ava-icon>
                                <span>Data Dump</span>
                            </button>
                        </div>
                    </div>

                    <!-- Right Side - Date Range Selector and Filter Button -->
                    <div class="right-calendar">
                        <ava-calendar
                            [isRange]="true"
                            [dateRange]="getCalendarDateRange()"
                            (rangeSelected)="onRangeSelected($event)">
                        </ava-calendar>
                        <ava-button variant="secondary" iconName="Funnel" iconPosition="only" size="medium"
                            class="filter-button" (userClick)="applyFilter()" title="Apply Filter">
                        </ava-button>
                    </div>
                </div>
            </div>

            <!-- Use Case Metrics -->
            <div class="metrics-container">
                <div class="metrics-row">
                    <app-analytics-txt-card [iconName]="'hourglass'" [title]="'Bugs Found'" [value]="useCaseMetrics?.totalBugs || 0"
                    [subtitle]="'Total bugs identified'"></app-analytics-txt-card>

                    <app-analytics-txt-card [iconName]="'shield-check'" [title]="'Unit Tests'" [value]="useCaseMetrics?.totalUnitTest || 0"
                    [subtitle]="'Unit tests created'"></app-analytics-txt-card>

                    <app-analytics-txt-card [iconName]="'library-big'" [title]="'Stories/Epics'" [value]="useCaseMetrics?.totalStory || 0"
                    [subtitle]="'Stories and epics managed'"></app-analytics-txt-card>

                    <app-analytics-txt-card [iconName]="'code'" [title]="'Code Optimizations'" [value]="useCaseMetrics?.totalCodeOptimization || 0"
                    [subtitle]="'Code optimizations performed'"></app-analytics-txt-card>

                    <app-analytics-txt-card [iconName]="'users'" [title]="'Active Users'" [value]="userActivity?.activeUsers || 0"
                    [subtitle]="'Currently active users'"></app-analytics-txt-card>
                </div>
            </div>

            <!-- Charts Section - Matching Digital Ascender Layout -->
            <div class="charts-grid">
                <!-- Row 1: User Consumption and Lines of Code -->
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>User Consumption</h3>
                        <ava-icon *ngIf="!noDataAvailable" iconName="Download" iconSize="20"
                            class="chart-download-icon" (click)="downloadExcel('userConsumption')"></ava-icon>
                    </div>
                    <div class="chart-content1">
                        <div *ngIf="usageLoader" class="loader-overlay">
                            <ava-cubical-loading background="blue"></ava-cubical-loading>
                        </div>
                        <div *ngIf="!usageLoader && noDataAvailable" class="no-data-message">
                            <div class="error-status">
                                <i class="fas fa-exclamation-triangle no-data-icon"></i>
                                <div class="no-data">No data available</div>
                            </div>
                        </div>
                        <div *ngIf="!usageLoader && !noDataAvailable" class="bars-container">
                            <div *ngFor="let user of sortedAnalytics" class="bar-container">
                                <div class="bar-wrapper">
                                    <div class="bar" [style.width.%]="getBarWidth(user.requestCount)">
                                        <span class="bar-label">{{ user.userSignature }}</span>
                                    </div>
                                    <div *ngIf="!user.requestCount" class="no-data-bar">No Data Available</div>
                                </div>
                                <span class="bar-value">{{ user.requestCount }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="chart-header">
                        <h3>Lines of code Processed</h3>
                        <ava-icon iconName="Download" iconSize="20" class="chart-download-icon"
                            (click)="downloadExcel('linesOfCodeProcessed')"></ava-icon>
                    </div>
                    <div *ngIf="usageLoader || chartLoadingStates['linesOfCodeProcessed']" class="loader-overlay">
                        <ava-cubical-loading background="blue"></ava-cubical-loading>
                    </div>
                    <highcharts-chart
                        *ngIf="!usageLoader && chartOptions['linesOfCodeProcessed'] && !chartLoadingStates['linesOfCodeProcessed']"
                        [Highcharts]="Highcharts" [options]="chartOptions['linesOfCodeProcessed']"
                        style="width: 100%; height: 300px;">
                    </highcharts-chart>
                </div>

                <!-- Row 2: Top 5 Individual Agents and Number of Requests -->
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>Top 5 Individual Agents</h3>
                        <ava-icon iconName="Download" iconSize="20" class="chart-download-icon"
                            (click)="downloadExcel('topUseCases')"></ava-icon>
                    </div>
                    <div *ngIf="usageLoader || chartLoadingStates['topUseCases']" class="loader-overlay">
                        <ava-cubical-loading background="blue"></ava-cubical-loading>
                    </div>
                    <highcharts-chart *ngIf="!usageLoader && chartOptions['topUseCases'] && !chartLoadingStates['topUseCases']"
                        [Highcharts]="Highcharts" [options]="chartOptions['topUseCases']"
                        style="width: 100%; height: 300px;">
                    </highcharts-chart>
                </div>

                <div class="chart-container">
                    <div class="chart-header">
                        <h3>Number of Requests</h3>
                        <ava-icon iconName="Download" iconSize="20" class="chart-download-icon"
                            (click)="downloadExcel('numberOfRequests')"></ava-icon>
                    </div>
                    <div *ngIf="usageLoader || chartLoadingStates['numberOfRequests']" class="loader-overlay">
                        <ava-cubical-loading background="blue"></ava-cubical-loading>
                    </div>
                    <highcharts-chart
                        *ngIf="!usageLoader && chartOptions['numberOfRequests'] && !chartLoadingStates['numberOfRequests']"
                        [Highcharts]="Highcharts" [options]="chartOptions['numberOfRequests']"
                        style="width: 100%; height: 300px;">
                    </highcharts-chart>
                </div>

                <!-- Row 3: Top 5 Languages/Frameworks and User Response -->
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>Top 5 Languages / Frameworks</h3>
                        <ava-icon iconName="Download" iconSize="20" class="chart-download-icon"
                            (click)="downloadExcel('topLanguages')"></ava-icon>
                    </div>
                    <div *ngIf="usageLoader || chartLoadingStates['topLanguages']" class="loader-overlay">
                        <ava-cubical-loading background="blue"></ava-cubical-loading>
                    </div>
                    <highcharts-chart *ngIf="!usageLoader && chartOptions['topLanguages'] && !chartLoadingStates['topLanguages']"
                        [Highcharts]="Highcharts" [options]="chartOptions['topLanguages']"
                        style="width: 100%; height: 300px;">
                    </highcharts-chart>
                </div>

                <div class="chart-container">
                    <div class="chart-header">
                        <h3>User Response</h3>
                        <ava-icon iconName="Download" iconSize="20" class="chart-download-icon"
                            (click)="downloadExcel('userResponse')"></ava-icon>
                    </div>
                    <div *ngIf="usageLoader || chartLoadingStates['userResponse']" class="loader-overlay">
                        <ava-cubical-loading background="blue"></ava-cubical-loading>
                    </div>
                    <highcharts-chart *ngIf="!usageLoader && chartOptions['userResponse'] && !chartLoadingStates['userResponse']"
                        [Highcharts]="Highcharts" [options]="chartOptions['userResponse']"
                        style="width: 100%; height: 300px;">
                    </highcharts-chart>
                </div>

                <!-- Row 4: % Dormant User and Response Time -->
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>% Dormant User</h3>
                        <ava-icon iconName="Download" iconSize="20" class="chart-download-icon"
                            (click)="downloadExcel('dormantUsers')"></ava-icon>
                    </div>
                    <div *ngIf="usageLoader || chartLoadingStates['userActivity']" class="loader-overlay">
                        <ava-cubical-loading background="blue"></ava-cubical-loading>
                    </div>
                    <highcharts-chart *ngIf="!usageLoader && chartOptions['userActivity'] && !chartLoadingStates['userActivity']"
                        [Highcharts]="Highcharts" [options]="chartOptions['userActivity']"
                        style="width: 100%; height: 300px;">
                    </highcharts-chart>
                </div>

                <div class="chart-container">
                    <div class="chart-header">
                        <h3>Response Time</h3>
                        <ava-icon iconName="Download" iconSize="20" class="chart-download-icon"
                            (click)="downloadExcel('responseTime')"></ava-icon>
                    </div>
                    <div *ngIf="usageLoader || chartLoadingStates['responseTime']" class="loader-overlay">
                        <ava-cubical-loading background="blue"></ava-cubical-loading>
                    </div>
                    <highcharts-chart *ngIf="!usageLoader && chartOptions['responseTime'] && !chartLoadingStates['responseTime']"
                        [Highcharts]="Highcharts" [options]="chartOptions['responseTime']"
                        style="width: 100%; height: 300px;">
                    </highcharts-chart>
                </div>
            </div>

            <!-- No Data Message -->
            <div *ngIf="noDataAvailable && !usageLoader" class="no-data-message">
                <i class="fas fa-chart-line"></i>
                <h3>No User Consumption Data Available</h3>
                <p>No user consumption data found for the selected date range.</p>
            </div>
        </div>

        <!-- Agents Tab Content -->
        <div *ngIf="activeTab === 'agents'" class="tab-content">

            <!-- Inline Date Filter -->
            <div class="analytics-controls" [formGroup]="vmFG">
                <div class="controls-row">
                    <!-- Left Side - Action Buttons -->
                    <div class="left-buttons">
                        <ava-button label="Langfuse" variant="secondary" size="medium" (userClick)="goToLangfuse()">
                        </ava-button>
                        <ava-button label="ICL Analytics" variant="secondary" size="medium"
                            (userClick)="goToIclAnalytics()">
                        </ava-button>

                        <!-- Download Button -->
                        <ava-button label="Download" iconPosition="left" size="medium" iconName="Download"
                            [iconSize]="20" variant="secondary" class="download-button"
                            (userClick)="onDownloadToggle($event)"
                            (click)="onDownloadToggle($event)">
                        </ava-button>
                        <div *ngIf="showDownloadOptions" class="download-dropdown">
                            <button class="dropdown-item" (click)="onPdfDownload()">
                                <ava-icon iconName="FileText"></ava-icon>
                                <span>Export PDF</span>
                            </button>
                            <button class="dropdown-item" (click)="onDataDownload()">
                                <ava-icon iconName="Database"></ava-icon>
                                <span>Data Dump</span>
                            </button>
                        </div>
                    </div>

                    <!-- Right Side - Date Range Selector and Filter Button -->
                    <div class="right-calendar">
                        <ava-calendar
                            [isRange]="true"
                            [dateRange]="getCalendarDateRange()"
                            (rangeSelected)="onRangeSelected($event)">
                        </ava-calendar>
                        <ava-button variant="secondary" iconName="Funnel" iconPosition="only" size="medium"
                            class="filter-button" (userClick)="applyFilter()" title="Apply Filter">
                        </ava-button>
                    </div>
                </div>
            </div>

            <!-- Collaborative Metrics -->
            <div class="metrics-container">
                <div class="metrics-row">
                    <app-analytics-txt-card [iconName]="'bot'" [title]="'Collaborative Agents Created'"
                        [value]="agentMetrics?.totalAgentsCreated || 0" [subtitle]="'Collaborative agents created'">
                    </app-analytics-txt-card>

                    <app-analytics-txt-card [iconName]="'rotate-ccw'" [title]="'Collaborative Agents Reused'"
                        [value]="agentMetrics?.totalAgentsReused || 0" [subtitle]="'Collaborative agents reused'">
                    </app-analytics-txt-card>

                    <app-analytics-txt-card [iconName]="'wrench'" [title]="'Total Tools'"
                        [value]="agentMetrics?.totalTools || 0" [subtitle]="'Tools available'">
                    </app-analytics-txt-card>

                    <app-analytics-txt-card [iconName]="'workflow'" [title]="'Workflow Execution Count'"
                        [value]="agentMetrics?.workflowExecutionCount || 0" [subtitle]="'Workflow executions'">
                    </app-analytics-txt-card>
                </div>
            </div>

            <!-- Charts Section - Matching Individual Analytics Layout -->
            <div class="charts-grid">
            <!-- Row 1: Studio Usage and Top 5 Collaborative Agents -->
            <div class="chart-container">
                <div class="chart-header">
                    <h3>Studio Usage</h3>
                    <ava-icon iconName="Download" iconSize="20" class="chart-download-icon"
                        (click)="downloadAgentExcel('studioUsage')"></ava-icon>
                </div>
                <div *ngIf="isLoading || chartLoadingStates['studioUsage']" class="loader-overlay">
                    <ava-cubical-loading background="blue"></ava-cubical-loading>
                </div>
                <highcharts-chart *ngIf="!isLoading && chartOptions['studioUsage'] && !chartLoadingStates['studioUsage']"
                    [Highcharts]="Highcharts" [options]="chartOptions['studioUsage']"
                    style="width: 100%; height: 300px;">
                </highcharts-chart>
            </div>

            <div class="chart-container">
                <div class="chart-header">
                    <h3>Top 5 Collaborative Agents</h3>
                    <ava-icon iconName="Download" iconSize="20" class="chart-download-icon"
                        (click)="downloadAgentExcel('topAgents')"></ava-icon>
                </div>
                <div *ngIf="isLoading || chartLoadingStates['topAgents']" class="loader-overlay">
                    <ava-cubical-loading background="blue"></ava-cubical-loading>
                </div>
                <highcharts-chart *ngIf="!isLoading && chartOptions['topAgents'] && !chartLoadingStates['topAgents']"
                    [Highcharts]="Highcharts" [options]="chartOptions['topAgents']" style="width: 100%; height: 300px;">
                </highcharts-chart>
            </div>

            <!-- Row 2: Collaborative Agent Created and Collaborative Agent Metrics -->
            <div class="chart-container">
                <div class="chart-header">
                    <h3>Collaborative Agent Created</h3>
                    <ava-icon iconName="Download" iconSize="20" class="chart-download-icon"
                        (click)="downloadAgentExcel('agentCreated')"></ava-icon>
                </div>
                <div *ngIf="isLoading || chartLoadingStates['agentCreated']" class="loader-overlay">
                    <ava-cubical-loading background="blue"></ava-cubical-loading>
                </div>
                <highcharts-chart *ngIf="!isLoading && chartOptions['agentCreated'] && !chartLoadingStates['agentCreated']"
                    [Highcharts]="Highcharts" [options]="chartOptions['agentCreated']"
                    style="width: 100%; height: 300px;">
                </highcharts-chart>
            </div>

            <div class="chart-container">
                <div class="chart-header">
                    <h3>Collaborative Agent Metrics</h3>
                    <ava-icon iconName="Download" iconSize="20" class="chart-download-icon"
                        (click)="downloadAgentExcel('agentMetrics')"></ava-icon>
                </div>

                <!-- Loading State -->
                <div *ngIf="isLoading || agentMetricsLoader" class="loader-overlay">
                    <ava-cubical-loading background="blue"></ava-cubical-loading>
                </div>

                <!-- No Data State -->
                <div *ngIf="!isLoading && agentMetricsNoDataAvailable && !agentMetricsLoader" class="no-data-message">
                    <div class="error-status">
                        <i class="fas fa-chart-line"></i>
                        <div class="no-data">No agent metrics data available</div>
                    </div>
                </div>

                <!-- Simple Table -->
                <div *ngIf="!isLoading && !agentMetricsNoDataAvailable && !agentMetricsLoader" class="agent-metrics-table-container">
                    <div class="table-wrapper">
                        <table class="agent-metrics-table">
                            <thead>
                                <tr>
                                    <th class="expand-column"></th>
                                    <th>Agent Name</th>
                                    <th>No. of time used</th>
                                </tr>
                            </thead>
                            <tbody>
                                <ng-container *ngFor="let agent of agentMetricsTableData; let i = index">
                                    <tr class="agent-row">
                                        <td class="expand-column">
                                            <button *ngIf="agent.workflows && agent.workflows.length > 0"
                                                class="expand-btn" (click)="toggleRowExpansion(i)"
                                                [class.expanded]="isRowExpanded(i)"
                                                [title]="isRowExpanded(i) ? 'Collapse workflows' : 'Expand workflows'">
                                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path *ngIf="!isRowExpanded(i)" d="M9 18L15 12L9 6"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                    <path *ngIf="isRowExpanded(i)" d="M6 9L12 15L18 9"
                                                        stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                </svg>
                                            </button>
                                        </td>
                                        <td class="agent-name">{{ agent.agentName }}</td>
                                        <td class="workflow-count">{{ agent.workflowCount }}</td>
                                    </tr>
                                    <!-- Expanded workflow details -->
                                    <tr *ngIf="isRowExpanded(i)" class="workflow-details-row">
                                        <td colspan="3">
                                            <div class="workflow-details">
                                                <div class="workflow-header">Workflows:</div>
                                                <div class="workflow-list">
                                                    <div *ngFor="let workflow of agent.workflows" class="workflow-item">
                                                        {{ workflow }}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </ng-container>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Row 3: Tool Analytics and Tool Usage -->
            <div class="chart-container">
                <div class="chart-header">
                    <h3>Tool Analytics</h3>
                    <ava-icon iconName="Download" iconSize="20" class="chart-download-icon"
                        (click)="downloadAgentExcel('toolAnalytics')"></ava-icon>
                </div>
                <div *ngIf="isLoading || chartLoadingStates['toolAnalytics']" class="loader-overlay">
                    <ava-cubical-loading background="blue"></ava-cubical-loading>
                </div>
                <highcharts-chart *ngIf="!isLoading && chartOptions['toolAnalytics'] && !chartLoadingStates['toolAnalytics']"
                    [Highcharts]="Highcharts" [options]="chartOptions['toolAnalytics']"
                    style="width: 100%; height: 300px;">
                </highcharts-chart>
            </div>

            <div class="tool-usage">
                <div class="tool-usage-container">
                    <div *ngIf="isLoading || chartLoadingStates['toolUsage']" class="loader-overlay">
                    <ava-cubical-loading background="blue"></ava-cubical-loading>
                    </div>
                    <ng-container *ngIf="!isLoading && !chartLoadingStates['toolUsage']">
                        <div class="tool-usage-header">
                            Tool Usage
                            <ava-icon *ngIf="toolUsage && toolUsage.length > 0" iconName="Download" iconSize="20"
                                class="chart-download-icon" (click)="downloadAgentExcel('toolUsage')"></ava-icon>
                        </div>
                        <div class="no-data-message" *ngIf="!toolUsage || toolUsage.length === 0">
                            <div class="error-status">
                                <i class="fas fa-exclamation-triangle no-data-icon"></i>
                                <div class="no-data">No data available</div>
                            </div>
                        </div>
                        <div *ngIf="toolUsage && toolUsage.length > 0" class="bars-container">
                            <div *ngFor="let tool of toolUsage.slice(0, 5)" class="bar-container">
                                <div class="bar-wrapper">
                                    <div class="bar tool-bar" [style.width.%]="getBarWidth(tool.usageCount, true)">
                                        <span class="bar-label">{{ tool.toolName }}</span>
                                    </div>
                                    <div *ngIf="!tool.usageCount" class="no-data-bar">No Data Available</div>
                                </div>
                                <span class="bar-value">{{ tool.usageCount }}</span>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </div>

            <!-- Row 4: Adoption Rate and User Consumption -->
            <div class="chart-container">
                <div class="chart-header">
                    <h3>Adoption Rate</h3>
                    <ava-icon iconName="Download" iconSize="20" class="chart-download-icon"
                        (click)="downloadAgentExcel('adoptionRate')"></ava-icon>
                </div>
                <div *ngIf="isLoading || chartLoadingStates['adoptionRate']" class="loader-overlay">
                    <ava-cubical-loading background="blue"></ava-cubical-loading>
                </div>
                <highcharts-chart *ngIf="!isLoading && chartOptions['adoptionRate'] && !chartLoadingStates['adoptionRate']"
                    [Highcharts]="Highcharts" [options]="chartOptions['adoptionRate']"
                    style="width: 100%; height: 300px;">
                </highcharts-chart>
            </div>

            <div class="chart-container">
                <div class="chart-header">
                    <h3>User Consumption</h3>
                    <ava-icon *ngIf="userConsumption && userConsumption.length > 0" iconName="Download" iconSize="20"
                        class="chart-download-icon" (click)="downloadAgentExcel('userConsumption')"></ava-icon>
                </div>
                <div class="chart-content">
                    <div *ngIf="isLoading || chartLoadingStates['userConsumption']" class="loader-overlay">
                    <ava-cubical-loading background="blue"></ava-cubical-loading>
                    </div>
                    <div *ngIf="!isLoading && !chartLoadingStates['userConsumption'] && (!userConsumption || userConsumption.length === 0)" class="no-data-message">
                        <div class="error-status">
                            <i class="fas fa-exclamation-triangle no-data-icon"></i>
                            <div class="no-data">No data available</div>
                        </div>
                    </div>
                    <div *ngIf="!isLoading && !chartLoadingStates['userConsumption'] && userConsumption && userConsumption.length > 0" class="bars-container">
                        <div *ngFor="let user of userConsumption" class="bar-container">
                            <div class="bar-wrapper">
                                <div class="bar" [style.width.%]="getBarWidth(user.consumptionCount || user.requestCount)">
                                    <span class="bar-label">{{ user.email || user.userSignature }}</span>
                                </div>
                                <div *ngIf="!(user.consumptionCount || user.requestCount)" class="no-data-bar">No Data Available</div>
                            </div>
                            <span class="bar-value">{{ user.consumptionCount || user.requestCount }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Row 5: % Dormant User -->
            <div class="chart-container">
                <div class="chart-header">
                    <h3>% Dormant User</h3>
                    <ava-icon iconName="Download" iconSize="20" class="chart-download-icon"
                        (click)="downloadAgentExcel('dormantUsers')"></ava-icon>
                </div>
                <div *ngIf="isLoading || chartLoadingStates['userActivity']" class="loader-overlay">
                    <ava-cubical-loading background="blue"></ava-cubical-loading>
                </div>
                <highcharts-chart *ngIf="!isLoading && chartOptions['userActivity'] && !chartLoadingStates['userActivity']"
                    [Highcharts]="Highcharts" [options]="chartOptions['userActivity']"
                    style="width: 100%; height: 300px;">
                </highcharts-chart>
            </div>
            </div>
        </div>
    </div>
</div>
