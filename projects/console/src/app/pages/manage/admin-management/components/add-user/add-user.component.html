<div class="user__management--wrapper">
  <div class="user__management--header">
    <ava-icon
      (userClick)="onExit()"
      [cursor]="true"
      [iconSize]="24"
      iconName="arrow-left"
      iconColor="#4C515B"
    ></ava-icon>
    <h2>Add User</h2>
  </div>
  <div class="user__management--container">
    <form [formGroup]="userManagementForm">
      <div class="basic__info--section">
        <!-- Email Field -->
        @if (!userDataLoading()) {
          <div class="input__field input">
            <label for="email" class="filter-label required">User Email</label>
            <ava-textbox
              formControlName="email"
              id="email"
              name="email"
              placeholder="Enter Email"
              [fullWidth]="false"
              size="md"
            >
            </ava-textbox>
          </div>
        } @else {
          <div class="input__field input">
            <div class="skeleton-loader skeleton-label"></div>
            <div class="skeleton-loader skeleton-input"></div>
          </div>
        }

        <!-- Role Field -->
        @if (!roleListLoading()) {
          <div class="input__field dropdown">
            <label for="role" class="filter-label required">Role</label>
            <ava-dropdown
              id="role"
              [options]="roleList()"
              dropdownTitle="Select a Role"
              [selectedValue]="selectedRoleValue"
              (selectionChange)="onRoleSelectionChange($event)"
            >
              <ava-option (click)="addNewRole()">
                <span>Create New Role +</span>
              </ava-option>
            </ava-dropdown>
          </div>
        } @else {
          <div class="input__field dropdown">
            <div class="skeleton-loader skeleton-label"></div>
            <div class="skeleton-loader skeleton-dropdown"></div>
          </div>
        }
      </div>

      <div class="access__control--container">
        <h3>Access Control</h3>
        @if (!pageListLoading()) {
          @for (page of pageList(); track page.pageId) {
            <div class="access__control--section">
              <label class="title" for="page.pageId">{{ page.pageName }}</label>
              <ava-dropdown
                class="dropdown"
                [id]="page.pageId"
                [disabled]="false"
                [selectedValues]="selectedValues[page.pageId.toString()]"
                dropdownTitle="Select a Category"
                [options]="actionList()"
                [checkboxOptions]="actionCheckboxList()"
                [disabled]="!isAccessControl()"
                (selectionChange)="
                  onSelectionChange($event, page.pageId.toString())
                "
              >
              </ava-dropdown>
              <div class="dropdown-tag">
                <label class="title-sm">Selected Values</label>
                <div class="tag--container">
                  @for (
                    tag of selectedTags[page.pageId.toString()];
                    track tag
                  ) {
                    <ava-tag
                      [label]="tag.name"
                      [customStyle]="{
                        background: '#E9F0FC',
                        color: '#2D3036',
                      }"
                      [pill]="true"
                      size="sm"
                      [removable]="selectedTagRemovable()"
                      (removed)="removeTag(tag, page.pageId.toString())"
                      iconColor="#000000"
                    ></ava-tag>
                  }
                </div>
              </div>
            </div>
          }
        } @else {
          <div class="skeleton-access-control">
            @for (item of [1, 2, 3]; track item) {
              <div class="skeleton-section">
                <div class="skeleton-loader skeleton-section-label"></div>
                <div class="skeleton-loader skeleton-dropdown"></div>
                <div
                  class="skeleton-loader skeleton-section-label"
                  style="width: 80px; margin-top: 12px"
                ></div>
              </div>
            }
          </div>
        }
      </div>

      <div class="realm-container">
        <h3 class="required">Assign Realm</h3>
        <div class="realm-section">
          @if (!realmsListLoading()) {
            <div class="realm__filter--section">
              <ava-autocomplete
                [options]="realmsList()"
                placeholder="Search for a Realms"
                startIcon="search"
                startIconColor="#6b7280"
                (optionSelected)="onOptionSelected($event)"
                [showDefaultOptions]="true"
                (valueChange)="onValueChange($event)"
              >
              </ava-autocomplete>
              <ava-button
                label="Add Realm"
                variant="primary"
                [customStyles]="{
                  background: '#E9F0FC',
                  color: '#11387C',
                  border: '1px solid #6898EB',
                }"
                size="large"
                (userClick)="openRealmPopup()"
                [width]="'100%'"
                class="action-button"
              >
              </ava-button>
            </div>
            <div class="realm tag--container">
              @for (tag of selectedRealmTag; track tag) {
                <ava-tag
                  [label]="tag.label"
                  [customStyle]="{ background: '#EDEDF3', color: '#2D3036' }"
                  [pill]="true"
                  size="sm"
                  [removable]="true"
                  iconColor="#2D3036"
                  (removed)="removeRealmTag(tag.value)"
                ></ava-tag>
              }
            </div>
          } @else {
            <div class="skeleton-realm-section">
              <div class="skeleton-loader skeleton-autocomplete"></div>
              <div class="skeleton-loader skeleton-button"></div>
            </div>
          }
        </div>
      </div>
    </form>
    <div class="button__container main">
      <ava-button
        label="Exit"
        variant="secondary"
        size="large"
        (userClick)="onExit()"
        [width]="'100%'"
        class="action-button"
      >
      </ava-button>
      <ava-button
        [label]="userId ? 'Update User' : 'Add User'"
        variant="primary"
        size="large"
        (userClick)="addNewUser()"
        [width]="'100%'"
        class="action-button"
        [disabled]="!isUserButtonEnabled()"
      >
      </ava-button>
    </div>
  </div>
</div>

<ava-popup
  [show]="showRealmPopup()"
  [showTitle]="false"
  [showHeaderIcon]="false"
  [showClose]="false"
  [showInlineMessage]="false"
  [showConfirm]="false"
  message=""
  [showCancel]="false"
  [popupWidth]="'744px'"
>
  <div class="add__realm--container">
    <form [formGroup]="addRealmForm" class="realm__form">
      @if (!orgDataLoading()) {
        <div class="form-fields">
          <div class="input__field--wrapper">
            <label class="filter-label required">Name of the Realm</label>
            <ava-textbox
              class="input-field"
              formControlName="realmName"
              id="realmName"
              name="realmName"
              placeholder="Enter Realm Name"
              [required]="true"
              [fullWidth]="false"
              size="md"
            >
            </ava-textbox>
          </div>
          <div class="input__field--wrapper"></div>
        </div>

        <div class="form-fields">
          <div class="input__field--wrapper">
            <label class="filter-label required">Choose Organization</label>
            <ava-dropdown
              [dropdownTitle]="'Select Organization'"
              [options]="orgOptions"
              [selectedValue]="selectedOrgName"
              [disabled]="false"
              (selectionChange)="onOrgSelect($event)"
              [search]="true"
              [enableSearch]="true"
            ></ava-dropdown>
          </div>
          <div class="input__field--wrapper">
            <label class="filter-label required">Choose Domain</label>
            <ava-dropdown
              [dropdownTitle]="'Select Domain'"
              [options]="domainOptions"
              [selectedValue]="selectedDomainName"
              [disabled]="!selectedOrg"
              (selectionChange)="onDomainSelect($event)"
              [search]="true"
              [enableSearch]="true"
            ></ava-dropdown>
          </div>
        </div>

        <div class="form-fields">
          <div class="input__field--wrapper">
            <label class="filter-label required">Choose Project</label>
            <ava-dropdown
              [dropdownTitle]="'Select Project'"
              [options]="projectOptions"
              [selectedValue]="selectedProjectName"
              [disabled]="!selectedDomain"
              (selectionChange)="onProjectSelect($event)"
              [search]="true"
              [enableSearch]="true"
            ></ava-dropdown>
          </div>
          <div class="input__field--wrapper">
            <label class="filter-label required">Choose Team</label>
            <ava-dropdown
              [dropdownTitle]="'Select Team'"
              [options]="teamOptions"
              [selectedValue]="selectedTeamName"
              [disabled]="!selectedProject"
              (selectionChange)="onTeamSelect($event)"
              [search]="true"
              [enableSearch]="true"
            ></ava-dropdown>
          </div>
        </div>
      } @else {
        <div class="form-fields">
          <div class="input__field--wrapper">
            <div class="skeleton-loader skeleton-label"></div>
            <div class="skeleton-loader skeleton-input"></div>
          </div>
          <div class="input__field--wrapper"></div>
        </div>

        <div class="form-fields">
          <div class="input__field--wrapper">
            <div class="skeleton-loader skeleton-label"></div>
            <div class="skeleton-loader skeleton-dropdown"></div>
          </div>
          <div class="input__field--wrapper">
            <div class="skeleton-loader skeleton-label"></div>
            <div class="skeleton-loader skeleton-dropdown"></div>
          </div>
        </div>

        <div class="form-fields">
          <div class="input__field--wrapper">
            <div class="skeleton-loader skeleton-label"></div>
            <div class="skeleton-loader skeleton-dropdown"></div>
          </div>
          <div class="input__field--wrapper">
            <div class="skeleton-loader skeleton-label"></div>
            <div class="skeleton-loader skeleton-dropdown"></div>
          </div>
        </div>
      }
    </form>
    <div class="button__container">
      <ava-button
        label="Cancel"
        variant="secondary"
        size="large"
        (userClick)="closeRealmPopup()"
        [width]="'100%'"
        class="action-button"
      >
      </ava-button>
      <ava-button
        label="Create"
        variant="primary"
        size="large"
        (userClick)="createRealm()"
        [width]="'100%'"
        class="action-button"
        [disabled]="!addRealmForm.valid"
      >
      </ava-button>
    </div>
  </div>
</ava-popup>

<ava-popup
  [show]="showCreateRolePopup()"
  [showTitle]="false"
  [showHeaderIcon]="false"
  [showClose]="false"
  [showInlineMessage]="false"
  [showConfirm]="false"
  message=""
  [showCancel]="false"
  [popupWidth]="'410px'"
>
  <div class="add__realm--container">
    <form [formGroup]="addRoleForm" class="realm__form">
      <div class="role__form-fields">
        <div class="input__field--wrapper">
          <label for="roleName" class="filter-label required"
            >Name of the Role</label
          >
          <ava-textbox
            formControlName="roleName"
            id="roleName"
            name="roleName"
            placeholder="Enter Role Name"
            [required]="true"
            [fullWidth]="false"
            size="md"
          >
          </ava-textbox>
        </div>

        <div class="input__field--wrapper">
          <label for="description" class="filter-label required"
            >Description</label
          >
          <ava-textbox
            formControlName="description"
            id="description"
            name="description"
            placeholder="Enter Description"
            [required]="true"
            [fullWidth]="false"
            size="md"
          >
          </ava-textbox>
        </div>
      </div>
    </form>
    <div class="button__container">
      <ava-button
        label="Cancel"
        variant="secondary"
        size="large"
        (userClick)="closeRealmPopup()"
        [width]="'100%'"
        class="action-button"
      >
      </ava-button>
      <ava-button
        label="Create"
        variant="primary"
        size="large"
        (userClick)="createRole()"
        [width]="'100%'"
        class="action-button"
        [disabled]="!addRoleForm.valid"
      >
      </ava-button>
    </div>
  </div>
</ava-popup>

<!-- success status popup for role and realm user -->
<ava-popup
  [show]="showStatusPopup()"
  [title]="'Success'"
  [message]="statusSuccessMessage"
  [showHeaderIcon]="true"
  headerIconName="check-circle"
  iconColor="#28a745"
  [showClose]="true"
  [showCancel]="false"
  (closed)="closeSuccessPopup()"
>
</ava-popup>

<!-- success popup for add and update user -->
<ava-popup
  [show]="showUserSuccessPopup"
  [title]="'Success'"
  [message]="userSuccessMessage"
  [showHeaderIcon]="true"
  headerIconName="check-circle"
  iconColor="#28a745"
  [showClose]="true"
  [showCancel]="false"
  [showConfirm]="true"
  [confirmButtonLabel]="'OK'"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="'#28a745'"
  (confirm)="onUserSuccessConfirm()"
  (closed)="onUserSuccessConfirm()"
>
</ava-popup>

<!-- Error Popup -->
<ava-popup
  [show]="showErrorPopup"
  [title]="'Failed'"
  [message]="popupErrorMessage"
  [showHeaderIcon]="true"
  headerIconName="alert-circle"
  iconColor="#dc3545"
  [showClose]="true"
  [showCancel]="false"
  [showConfirm]="true"
  [confirmButtonLabel]="'OK'"
  [confirmButtonVariant]="'primary'"
  [confirmButtonBackground]="'#dc3545'"
  (confirm)="closeErrorPopup()"
  (closed)="closeErrorPopup()"
>
</ava-popup>
