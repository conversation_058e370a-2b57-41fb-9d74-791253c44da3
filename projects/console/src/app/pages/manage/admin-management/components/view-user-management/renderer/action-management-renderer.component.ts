import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { IconComponent } from '@ava/play-comp-library';
import { CellRenderer, CellRendererParams } from 'projects/console/src/app/shared/components/table-grid/model/table-grid.model';

@Component({
  selector: 'app-admin-management-cell',
  standalone: true,
  imports: [CommonModule, IconComponent],
  template: `
    @if (params.colDef.header === 'Name') {
      <div class="user__cell flex" (click)="onCellClick()">
        <div class="profile-trigger">
          <img [src]="userAvatar" alt="User Profile" class="profile-avatar" />
        </div>
        <div class="name__container flex">
          <div class="name-title" >{{ params.value }}</div>
          <div class="sub-title">{{ params.rowData.email }}</div>
        </div>
      </div>
    } @else {
      <ava-icon
        [cursor]="true"
        (click)="deleteUser()"
        [iconSize]="16"
        iconName="trash-2"
        iconColor="#1C1B1F"
      ></ava-icon>
    }
  `,
  styles: [
    `
    .flex {
      display: flex;
    }
    .user__cell {
      cursor: pointer;
      gap: 16px;
    }
    .name-title {
      font-weight: 700;
    }
    .name__container {
      flex-direction: column;
      gap: 4px;
    }
    .sub-title {
      font-size: 14px;
      color: #616874;
    }
    .profile-trigger {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      transition: all 0.2s ease;
      padding: 2px;

      &:hover {
        background: rgba(0, 0, 0, 0.05);
      }

      &.active {
        background: rgba(0, 0, 0, 0.1);
      }

      .profile-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid rgba(255, 255, 255, 0.2);
      }
    }
    `,
  ],
})
export class AdminManagementCellComponent implements CellRenderer {
  params!: any;
  userAvatar: string = '';

  aweInit(params: CellRendererParams) {
    this.params = params;
    this.generateUserAvatar()
  }

  deleteUser() {
    this.params.context.componentParent.deleteUser(this.params.rowData.userId);
  }

  onCellClick() {
    this.params.context.componentParent.openPreviewPanel(this.params.rowData);
  }

  private generateUserAvatar(): void {
    // Generate avatar from user initials if no profile picture is available
    if (this.params.value) {
      const nameParts = this.params.value.trim().split(' ');
      let initials = '';

      if (nameParts.length >= 2) {
        // First letter of first name and first letter of last name
        initials = nameParts[0][0] + nameParts[nameParts.length - 1][0];
      } else if (nameParts.length === 1) {
        // Just first letter if only one name
        initials = nameParts[0][0];
      } else {
        initials = 'U'; // Default to 'U' for User
      }

      initials = initials.toUpperCase();

      // Generate a colored avatar with initials
      const colors = [
        '#8B5CF6',
        '#06B6D4',
        '#10B981',
        '#F59E0B',
        '#EF4444',
        '#8B5A2B',
        '#6366F1',
        '#EC4899',
      ];
      const colorIndex = this.params.value.length % colors.length;
      const backgroundColor = colors[colorIndex];

      this.userAvatar = `data:image/svg+xml;base64,${btoa(`
        <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
          <circle cx="20" cy="20" r="20" fill="${backgroundColor}"/>
          <text x="20" y="26" font-family="Inter, Arial, sans-serif" font-size="14" font-weight="600" fill="white" text-anchor="middle">${initials}</text>
        </svg>
      `)}`;
    }
  }
}
