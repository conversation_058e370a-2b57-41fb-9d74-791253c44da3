import { CommonModule } from '@angular/common';
import { Component, inject, Input } from '@angular/core';
import {
  AvaTagComponent,
  ButtonComponent,
  IconComponent,
} from '@ava/play-comp-library';
import { User } from '../../../models/user-management.model';
import { Router } from '@angular/router';
import { PreviewPanelComponent } from '@shared/index';

@Component({
  selector: 'app-user-management-preview-panel',
  standalone: true,
  imports: [
    ButtonComponent,
    IconComponent,
    CommonModule,
    PreviewPanelComponent,
    AvaTagComponent,
  ],
  template: `
    <div class="backdrop" (click)="closePreview()"></div>
    <app-preview-panel
      class="panel-container"
      (click)="$event.stopPropagation()"
      [divider]="false"
    >
      <div panel-header class="preview-header">
        <h2 class="panel-title">Metadata Information</h2>
        <ava-icon
          iconName="x"
          [cursor]="true"
          iconColor="black"
          class="close-btn"
          (click)="closePreview()"
        ></ava-icon>
      </div>

      <div panel-content class="preview-body">
        <div class="row-container">
          <div>
            <div class="meta-label">User Name</div>
            <div class="meta-value">{{ metaData?.userName }}</div>
          </div>
          <div>
            <div class="meta-label">Email</div>
            <div class="meta-value">{{ metaData?.email }}</div>
          </div>
          <div>
            <div class="meta-label">Role</div>
            <div class="meta-value">{{ metaData?.roles }}</div>
          </div>
          <div>
            <div class="meta-label">Authorized By</div>
            <div class="meta-value">{{ metaData?.authorizedBy }}</div>
          </div>
          <div>
            <div class="meta-label">Created On</div>
            <div class="meta-value">{{ metaData?.createdAt }}</div>
          </div>
        </div>
        <div class="realm-container">
          <div class="realm-title">Realms Assigned</div>
          <div class="realm-tag">
            @for (realm of metaData?.realms; track realm) {
              <ava-tag
                [label]="realm"
                [customStyle]="{ background: '#EDEDF3', color: '#2D3036' }"
                [pill]="true"
                size="sm"
              ></ava-tag>
            }
          </div>
        </div>
      </div>

      <div panel-footer>
        <div class="action__button--container">
          <ava-button
            label="Remove User"
            variant="primary"
            [customStyles]="{
              background: 'white',
              color: '#DC2626',
              border: '1px solid #DC2626',
            }"
            size="large"
            iconName="trash"
            iconPosition="right"
            iconColor="#DC2626"
            (userClick)="onRemoveUser()"
            [width]="'100%'"
            class="action-button"
          >
          </ava-button>
          <ava-button
            label="Edit User Details"
            variant="primary"
            [customStyles]="{
              background:
                'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
              '--button-effect-color': '33, 90, 214',
            }"
            size="large"
            iconName="pencil"
            iconPosition="right"
            iconColor="white"
            (userClick)="onUpdateUser()"
            [width]="'100%'"
            class="action-button"
          >
          </ava-button>
        </div>
      </div>
    </app-preview-panel>
  `,
  styles: [
    `
      .backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 999;
        cursor: pointer;
      }
      .panel-container {
        position: relative;
        z-index: 1000;
      }
      .preview-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        min-height: 56px;
        border-bottom: 1px solid #9ca1aa;
        .panel-title {
          font-weight: 600;
          color: #111;
          text-align: left;
          margin: 0;
          line-height: 1.2;
          flex: 1 1 auto;
        }
      }
      .preview-body {
        margin-top: 20px;
        .row-container {
          display: flex;
          flex-direction: column;
          gap: 24px;
        }
      }
      .meta-label,
      .meta-value {
        font-size: 1rem;
        font-weight: 400;
        color: #111;
        font-family: 'Mulish';
      }
      .meta-label {
        font-weight: 700;
      }
      .realm-container {
        margin-top: 40px;
        .realm-title {
          font-size: 1.5rem;
          font-weight: 700;
          color: #111;
        }
        .realm-tag {
          display: flex;
          gap: 8px;
          margin: 10px 0 0 0;
          flex-wrap: wrap;
        }
      }
      .action__button--container {
        display: flex;
        gap: 12px;
        justify-content: space-between;
        .action-button {
          width: 100%;
        }
      }
    `,
  ],
})
export class UserManagementPreviewPanelComponent {
  @Input() metaData?: User;
  @Input() closePreview!: () => void;
  @Input() onParentAction?: (id: string | number) => void;

  private router = inject(Router);

  onButtonClick(event: any): void {
    this.closePreview();
  }

  onUpdateUser() {
    this.closePreview();
    this.router.navigate(['manage/admin-management/add-user'], {
      queryParams: { id: this.metaData?.userId, mode: 'edit' },
    });
  }

  onRemoveUser() {
    if (this.onParentAction && this.metaData) {
      this.closePreview();
      this.onParentAction(this.metaData.userId);
    }
  }
}
