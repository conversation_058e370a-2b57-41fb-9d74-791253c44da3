export interface User {
  authorizedBy: string | null;
  createdAt: string;
  email: string;
  isActive: 'Active' | 'Inactive' | string;
  roles: string;
  userId: number;
  userName: string;
  realms: string | null;
}

export interface Page {
  pageId: number;
  pageName: string;
  description?: string;
  createdAt?: string;
}

export interface DropdownActionOption {
  name: string;
  value: number;
}