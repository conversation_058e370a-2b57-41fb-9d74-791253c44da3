#search-filter-container {
  align-items: center;
}

.mt-5 {
  margin-top: 2rem;
}

.tag--container {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.realm__form {
  display: flex;
  flex-flow: column;
  gap: 16px;    
}

.form-fields {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.input__field--wrapper {
  flex: 1;
  width: 360px;
}

.filter-label {
  margin-bottom: 4px;
  display: block;
  font-weight: 500;
  text-align: left;
  color: #14161F;
}

.required::after {
  content: " *";
  color: red;
  font-weight: bold;
}

.button__container {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin: 16px 0 16px 0;
}
