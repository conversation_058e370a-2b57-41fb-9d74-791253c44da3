/**
* =========================================================================
* Play+ Design System: Console Theme
*
* Console theme overrides for semantic tokens.
* This theme uses a blue and aqua color scheme for a modern console look.
* Primary: Blue (#2563EB) | Secondary: Aqua (#03BDD4)
* =========================================================================
*/

[data-theme="light"] {
  /* --- Console Theme Color Overrides --- */
  --background-image: url("../../../public/svgs/background.svg");

  /* New Global Colors for Console Theme */
  --global-color-blue-500: #2563eb;
  --global-color-blue-700: #1d4ed8;
  --global-color-blue-400: #339dff;
  --global-color-blue-900: #1e3a8a;
  --global-color-blue-100: #dbeafe;

  --global-color-aqua-500: #03bdd4;
  --global-color-aqua-700: #0891b2;
  --global-color-aqua-900: #164e63;
  --textbox-label-color: #4c515b;

  /* PRIMARY (Vibrant Blue) */
  --color-brand-primary: var(--global-color-blue-500);
  --color-brand-primary-hover: var(--global-color-blue-700);
  --color-brand-primary-active: var(--global-color-blue-900);
  --color-surface-interactive-primary: var(--global-color-blue-500);
  --color-surface-interactive-primary-hover: var(--global-color-blue-700);
  --color-surface-interactive-primary-active: var(--global-color-blue-500);
  --color-border-primary: var(--global-color-blue-500);
  --color-border-primary-hover: var(--global-color-blue-700);
  --color-border-primary-active: var(--global-color-blue-900);
  --color-text-primary: var(--global-color-gray-700);
  --color-text-on-primary: var(--global-color-white);

  /* SECONDARY (Aqua) */
  --color-brand-secondary: var(--global-color-aqua-500);
  --color-brand-secondary-hover: var(--global-color-aqua-700);
  --color-brand-secondary-active: var(--global-color-aqua-900);
  --color-surface-interactive-secondary: var(--global-color-aqua-500);
  --color-surface-interactive-secondary-hover: var(--global-color-aqua-700);
  --color-surface-interactive-secondary-active: var(--global-color-aqua-900);
  --color-border-secondary: var(--global-color-aqua-500);
  --color-border-secondary-hover: var(--global-color-aqua-500);
  --color-border-secondary-active: var(--global-color-aqua-500);
  --color-text-secondary: var(--global-color-aqua-500);
  --color-text-on-secondary: var(--global-color-white);
  --color-background-secondary: var(--global-color-blue-100);

  /* BUTTONS, TABS, TAGS: Use these tokens for all secondary/primary states */
  --color-text-placeholder: var(--global-color-gray-400);
  --color-text-disabled: var(--global-color-gray-400);
  --color-text-on-brand: var(--global-color-white);
  --color-text-interactive: var(--global-color-blue-500);
  --color-text-interactive-hover: var(--global-color-blue-700);
  --color-text-success: var(--global-color-green-500);
  --color-text-error: var(--global-color-red-500);
  --color-background-primary: var(--global-color-white);
  --color-background-disabled: var(--global-color-gray-100);
  --color-surface-interactive-default: var(--global-color-blue-500);
  --color-surface-interactive-hover: var(--global-color-blue-700);
  --color-surface-interactive-active: var(--global-color-blue-700);
  --color-surface-disabled: var(--global-color-gray-200);
  --color-surface-subtle-hover: var(--global-color-gray-100);
  --color-border-default: var(--global-color-gray-300);
  --color-border-subtle: var(--global-color-gray-200);
  --color-border-interactive: var(--global-color-blue-500);
  --color-border-focus: var(--global-color-blue-500);
  --color-border-error: var(--global-color-red-500);
  --color-background-error: var(--global-color-red-500);

  /* Semantic Border Colors */
  --color-border-warning: var(--global-color-yellow-500);
  --color-border-success: var(--global-color-green-500);
  --color-border-info: var(--global-color-blue-info-500);

  /* Semantic Text Colors */
  --color-text-warning: var(--global-color-yellow-600);
  --color-text-success: var(--global-color-green-600);
  --color-text-info: var(--global-color-blue-info-500);

  /* Semantic Background Colors */
  --color-background-warning: var(--global-color-yellow-500);
  --color-background-success: var(--global-color-green-500);
  --color-background-info: var(--global-color-blue-info-500);

  /* --- Console Theme Glassmorphism --- */
  --glass-backdrop-blur: 12px;
  --glass-background-color: rgba(255, 255, 255, 0.25);
  --glass-border-color: rgba(255, 255, 255, 0.3);
  --glass-border-width: 1px;
  --glass-elevation: var(--global-elevation-02);

  /* =======================
   CONSOLE THEME: RGB OVERRIDES
   Extract RGB values from console theme semantic colors
   ======================= */
  --rgb-brand-primary: 37, 99, 235;
  /* From #2563EB */
  --rgb-brand-secondary: 3, 189, 212;
  /* From #03BDD4 */
  --rgb-brand-tertiary: 37, 99, 235;
  /* From #2563EB */
  --rgb-brand-quaternary: 3, 189, 212;
  /* From #03BDD4 */
  --rgb-brand-quinary: 67, 189, 144;
  /* From #43bd90 */
  --rgb-brand-senary: 250, 112, 154;
  /* From #fa709a */
  --rgb-violet: 124, 58, 237;
  /* From #7c3aed */
  --rgb-royal-blue: 37, 99, 235;
  /* From #2563EB */
  --rgb-cyan: 3, 189, 212;
  /* From #03BDD4 */
  --rgb-spearmint: 67, 189, 144;
  /* From #43bd90 */
  --rgb-rose: 250, 112, 154;
  /* From #fa709a */
  --rgb-white: 255, 255, 255;
  /* From #ffffff */
  --rgb-black: 0, 0, 0;
  /* From #000000 */
  --rgb-neutral-100: 243, 244, 246;
  /* From #f3f4f6 */

  /* =======================
   CONSOLE THEME: EFFECT COLOR OVERRIDES
   Override all effect colors for proper console theme adaptation
   ======================= */
  --effect-color-primary: var(--rgb-brand-primary);
  /* Blue - console primary */
  --effect-color-secondary: var(--rgb-brand-secondary);
  /* Aqua - console secondary */
  --effect-color-accent: var(--rgb-violet);
  /* Violet accent - keep consistent */
  --effect-color-neutral: var(--rgb-black);
  /* Black shadows work well on light bg */
  --effect-color-surface: var(--rgb-white);
  /* White glass/shimmer on light bg */

  /* =======================
   CONSOLE THEME: PERSONALITY OVERRIDES
   Only override personality tokens that need console theme adjustments
   ======================= */
  /* Most personalities use base values, only override if console theme needs different intensity */
  /* Base personalities work well for console theme, no overrides needed currently */

  /* =======================
   CONSOLE THEME: SEMANTIC COMPONENT TOKENS
   Theme-aware component-specific tokens using the metaphor system
   ======================= */

  /* Glass Metaphor (Theme-Aware) */
  --surface-glass-bg: rgba(var(--effect-color-surface), 0.15);
  --surface-glass-border: rgba(var(--effect-color-surface), 0.7);
  --surface-glass-shadow: rgba(var(--effect-color-surface), 0.18);

  /* Light Metaphor (Theme-Aware) */
  --color-light-glow: rgba(var(--effect-color-primary), 0.45);
  --color-light-glow-focus: rgba(var(--effect-color-primary), 0.65);
  --color-light-shadow: rgba(var(--effect-color-accent), 0.1);
  --color-light-border: var(--color-brand-primary);
  --color-light-border-focus: var(--color-brand-secondary);

  /* Liquid Metaphor (Theme-Aware) */
  --color-liquid-shimmer-start: var(--color-brand-secondary);
  --color-liquid-shimmer-end: var(--color-brand-primary);

  /* =======================
   CONSOLE THEME: SOPHISTICATED GLASS CHAINING
   Override glass surface colors for console theme variants
   ======================= */

  /* Console theme glass surface - keep default white */
  --glass-surface-color: var(--rgb-white);

  /* Console theme variant glass colors */
  --glass-variant-primary: var(--rgb-brand-primary);
  --glass-variant-success: 76, 175, 80;
  --glass-variant-warning: 255, 152, 0;
  --glass-variant-danger: 244, 67, 54;
  --glass-variant-info: 33, 150, 243;

  /* Custom variant example - Add new colors here */
  --glass-variant-purple: 156, 39, 176;
  /* Custom purple variant */
  --glass-variant-emerald: 16, 185, 129;
  /* Custom emerald variant */
  --glass-variant-blue: 37, 99, 235;
  /* Console blue variant */
  --glass-variant-aqua: 3, 189, 212;
  /* Console aqua variant */

  /* Console theme effect color adjustments */
  --effect-color-neutral: var(--rgb-black);
  /* Black shadows work on light bg */
  --effect-color-surface: var(--rgb-white);
  /* White highlights on light bg */

  /* =======================
   CONSOLE THEME: CONSOLE CARD COMPONENT COLORS
   Color variables for console card component (White & Blue Variants)
   ======================= */

  /* Primary Colors */
  --console-card-primary-text: #2f5a8e;
  --console-card-primary-title: #1a46a7;
  --console-card-primary-icon: #e91e63;
  --console-card-primary-border: #2f5a8e;
  --execution-header-background: #cfe7fd;
  --plaground-execution-border: #f1f1f3;
  --console-card-bg: #ffffff;
  --console-card-bg-hover: #f8fafc;
  --console-card-bg-disabled: rgba(255, 255, 255, 0.95);

  /* Shadow Colors */
  --console-card-shadow: rgba(135, 161, 151, 0.12);
  --console-card-shadow-hover: rgba(0, 0, 0, 0.2);
  --console-card-shadow-focus: rgba(47, 90, 142, 0.15);
  --console-card-shadow-active: rgba(47, 90, 142, 0.2);

  /* Interactive Colors */
  --console-card-button-color: #6b7280;
  --console-card-button-hover-color: #2f5a8e;
  --console-card-button-focus-outline: #2f5a8e;

  /* Tooltip Colors */
  --console-card-tooltip-bg: #1f2937;
  --console-card-tooltip-shadow: rgba(0, 0, 0, 0.15);

  /* Loading Colors */
  --console-card-loading-border: #e0e0e0;
  --console-card-loading-spinner: #e91e63;

  /* Skeleton Colors */
  --console-card-skeleton-start: #f0f0f0;
  --console-card-skeleton-middle: #e0e0e0;
  --console-card-skeleton-end: #f0f0f0;
  --console-card-skeleton-button-start: rgba(47, 90, 142, 0.25);
  --console-card-skeleton-button-middle: rgba(26, 70, 167, 0.25);
  --console-card-skeleton-button-end: rgba(47, 90, 142, 0.25);

  /* =======================
   CONSOLE THEME: PAGE FOOTER COMPONENT COLORS
   Color variables for page-footer component (White & Blue Variants)
   ======================= */

  /* Background Colors */
  --page-footer-bg: linear-gradient(
    102.14deg,
    rgba(255, 255, 255, 0.8) 1.07%,
    rgba(255, 255, 255, 0.9) 98.01%
  );
  --page-footer-bg-hover: linear-gradient(
    102.14deg,
    rgba(255, 255, 255, 0.9) 1.07%,
    rgba(255, 255, 255, 1) 98.01%
  );
  --page-footer-bg-disabled: rgba(255, 255, 255, 0.6);

  --agent-execution-user-input-text: var(--global-color-blue-400);
  /* Border Colors */
  --page-footer-border: rgba(230, 230, 230, 0.5);
  --page-footer-border-hover: rgba(47, 90, 142, 0.2);

  /* Shadow Colors */
  --page-footer-shadow: rgba(0, 0, 0, 0.03);
  --page-footer-shadow-hover: rgba(0, 0, 0, 0.08);

  /* Text Colors */
  --page-footer-text: #2f5a8e;
  --page-footer-text-secondary: #6b7280;

  /* =======================
   CONSOLE THEME: PAGINATION COMPONENT COLORS
   Color variables for pagination component (White & Blue Variants)
   ======================= */

  /* Primary Colors */
  --pagination-primary-text: #1a46a7;
  --pagination-primary-text-hover: #143681;
  --pagination-primary-bg: #e9effd;
  --pagination-primary-bg-hover: #bbcff9;
  --pagination-primary-border: #1a46a7;

  /* Interactive Colors */
  --pagination-button-bg: transparent;
  --pagination-button-bg-hover: #bbcff9;
  --pagination-button-text: #1a46a7;
  --pagination-button-text-hover: #143681;
  --pagination-button-border: transparent;
  --pagination-button-border-hover: #1a46a7;

  /* Active State Colors */
  --pagination-active-bg: #bbcff9;
  --pagination-active-text: #143681;
  --pagination-active-border: #1a46a7;

  /* Disabled State Colors */
  --pagination-disabled-opacity: 0.5;
  --pagination-disabled-text: #9ca3af;
  --pagination-disabled-bg: #f3f4f6;

  /* Ellipsis Colors */
  --pagination-ellipsis: var(--pagination-primary-text);

  /* Focus States */
  --pagination-focus-outline: #1a46a7;
  --pagination-focus-ring: rgba(26, 70, 167, 0.2);

  /*AVA Text Cards*/
  --card-background-gradient: linear-gradient(118deg, #7fc2eb, #5b92ea 89.27%);
  --card-box-shadow: 0 4px 12px #87a1971f;

  /* Required Field Colors */
  --agents-preview-required: #ef4444;

  /* =======================
   CONSOLE THEME: BUILD AGENTS COMPONENT COLORS
   Color variables for build-agents component (White & Blue Variants)
   ======================= */

  /* Top Navigation Bar Colors */
  --build-agents-nav-bg: #e6f3ff;
  --build-agents-nav-border: white;
  --build-agents-back-btn-text: #616874;
  --console-blue-100: #b0d9ff;
  --build-agents-back-btn-hover-bg: #f3f4f6;
  --build-agents-back-btn-hover-text: #1f2937;

  /* Agent Type Toggle Colors */
  --build-agents-toggle-bg: #f3f4f6;
  --build-agents-toggle-btn-text: #3b3f46;
  --build-agents-toggle-btn-hover: #374151;
  --build-agents-toggle-active-bg: linear-gradient(
    103.35deg,
    rgb(33, 90, 214) 31.33%,
    rgb(3, 189, 212) 100%
  );
  --build-agents-toggle-active-text: #ffffff;
  --build-agents-toggle-shadow: rgba(0, 0, 0, 0.1);

  /* Agent Details Floater Colors */
  --build-agents-floater-bg: #ffffff;
  --build-agents-floater-border: #e5e7eb;
  --build-agents-floater-shadow: rgba(0, 0, 0, 0.1);
  --build-agents-floater-header-bg: #ffffff;
  --build-agents-floater-header-hover: #f9fafb;
  --build-agents-floater-header-border: #f3f4f6;
  --build-agents-floater-title: #1f2937;
  --build-agents-field-label: #374151;

  /* Configure Agent Panel Colors */
  --build-agents-panel-bg: #ffffff;
  --build-agents-panel-border: #ffffff;
  --build-agents-panel-shadow: rgba(0, 0, 0, 0.08);
  --build-agents-panel-header-bg: #ffffff;
  --build-agents-panel-header-hover: #f9fafb;
  --build-agents-panel-title: #4c515b;
  --build-agents-panel-border-light: #f3f4f6;

  /* Tab Colors */
  --build-agents-tab-bg: #f9fafb;
  --build-agents-tab-border: #d1d5db;
  --build-agents-tab-icon: #6b7280;
  --build-agents-tab-label: #6b7280;
  --build-agents-tab-active-bg: #3b82f6;
  --build-agents-tab-active-border: #3b82f6;
  --svg-icon-color: #19356c;
  --build-agents-tab-active-icon: #ffffff;
  --build-agents-tab-active-label: #111827;

  /* Search Section Colors */
  --build-agents-search-bg: #f9fafb;
  --build-agents-search-border: #e5e7eb;
  --build-agents-search-focus-border: #3b82f6;
  --build-agents-search-focus-shadow: rgba(59, 130, 246, 0.1);
  --build-agents-search-text: #111827;
  --build-agents-search-placeholder: #9ca3af;

  /* Tool Item Colors */
  --build-agents-tool-bg: #ffffff;
  --build-agents-tool-border: #f0f1f2;
  --build-agents-tool-shadow: rgba(0, 0, 0, 0.05);
  --build-agents-tool-shadow-alt: rgba(0, 0, 0, 0.08);
  --build-agents-tool-hover-border: #bbcff9;
  --build-agents-tool-hover-shadow: rgba(0, 0, 0, 0.1);
  --build-agents-tool-icon-bg: #e9effd;
  --build-agents-tool-icon: #ffffff;
  --build-agents-tool-name: #3b3f46;
  --build-agents-tool-count: #4c515b;
  --build-agents-tool-description: #616874;

  /* Create Tool Section Colors */
  --build-agents-create-gradient: linear-gradient(
    135deg,
    #3b82f6 0%,
    #1d4ed8 100%
  );
  --build-agents-create-hover-gradient: linear-gradient(
    135deg,
    #2563eb 0%,
    #1e40af 100%
  );
  --build-agents-create-shadow: rgba(59, 130, 246, 0.3);

  /* Canvas Colors */
  --build-agents-canvas-bg: var(--background-secondary);
  --build-agents-canvas-edge: #9ca1aa;
  --build-agents-canvas-marker: #9ca1aa;
  --build-agents-canvas-arrow: #9ca1aa;
  --build-agents-canvas-node-shadow: rgba(0, 0, 0, 0.1);
  --build-agents-canvas-node-hover-shadow: rgba(0, 0, 0, 0.15);
  --build-agents-canvas-node-border: #e5e7eb;
  --build-agents-canvas-node-bg: linear-gradient(
    135deg,
    #ffffff 0%,
    #f8fafc 100%
  );
  --build-agents-canvas-node-hover-border: #4f46e5;
  --build-agents-canvas-node-hover-bg: linear-gradient(
    135deg,
    #ffffff 0%,
    #f1f5f9 100%
  );
  --build-agents-canvas-node-selected-border: #4f46e5;
  --build-agents-canvas-node-selected-bg: linear-gradient(
    135deg,
    #eef2ff 0%,
    #e0e7ff 100%
  );
  --build-agents-canvas-node-selected-shadow: rgba(79, 70, 229, 0.1);

  /* Agent Details Dropdown Colors */
  --build-agents-dropdown-bg: #ffffff;
  --build-agents-dropdown-border: #e5e7eb;
  --build-agents-dropdown-shadow: rgba(0, 0, 0, 0.1);
  --build-agents-dropdown-hover-border: #4f46e5;
  --build-agents-dropdown-hover-shadow: rgba(79, 70, 229, 0.1);
  --build-agents-dropdown-focus-border: #4f46e5;
  --build-agents-dropdown-focus-shadow: rgba(79, 70, 229, 0.2);

  /* Execute Mode Colors */
  --build-agents-execute-border: #e5e7eb;
  --build-agents-execute-connection: #2563eb;
  --build-agents-execute-arrow: #2563eb;

  /* Playground Controls Colors */
  --build-agents-playground-bg: #f8f9fa;
  --build-agents-playground-border: #e9ecef;
  --build-agents-playground-title: #495057;
  --build-agents-clear-chat-bg: #dc3545;
  --build-agents-clear-chat-hover: #c82333;
  --build-agents-file-upload-bg: #007bff;
  --build-agents-file-upload-hover: #0056b3;
  --build-agents-file-item-bg: #e9ecef;
  --build-agents-file-item-border: #ced4da;
  --build-agents-file-remove: #6c757d;
  --build-agents-file-remove-hover: #dc3545;

  /* Modal Colors */
  --build-agents-modal-error: #dc3545;

  /* Pulsating Animation Colors */
  --build-agents-pulsating-border: #e5e7eb;
  --build-agents-pulsating-shadow: rgba(0, 0, 0, 0.1);
  --build-agents-pulsating-active-border: #f74646;
  --build-agents-pulsating-active-shadow: rgba(247, 70, 70, 0.3);
  --build-agents-pulsating-active-ring: rgba(247, 70, 70, 0.2);

  /* Tool Icon Colors */
  --build-agents-tool-icon-stroke: #00498c;

  /* =======================
   CONSOLE THEME: CUSTOM TABS COMPONENT COLORS
   Color variables for custom-tabs component (White & Blue Variants)
   ======================= */

  /* Tab Item Colors */
  --custom-tabs-bg: transparent;
  --custom-tabs-border: #d1d5db;
  --custom-tabs-text: #000000;
  --custom-tabs-text-active: #000000;

  /* Active Tab Colors */
  --custom-tabs-active-bg: #e9effd;
  --custom-tabs-active-border: #0084ff;
  --custom-tabs-active-icon: #215ad6;
  --custom-tabs-active-icon-filter: brightness(0) saturate(100%) invert(27%)
    sepia(51%) saturate(2878%) hue-rotate(214deg) brightness(97%) contrast(87%);

  /* Disabled Tab Colors */
  --custom-tabs-disabled-opacity: 0.5;

  /* Tab Icon Box Colors */
  --custom-tabs-icon-box-bg: transparent;
  --custom-tabs-icon-box-border: #8ac6ff;
  --custom-tabs-icon-box-border-radius: 8px;

  /* Tab Label Colors */
  --custom-tabs-label-text: #000000;
  --custom-tabs-label-active-text: #000000;
  --custom-tabs-label-font-weight: 500;
  --custom-tabs-label-active-font-weight: 500;

  // Custom Create Models
  --list-bg-color-selected: #b0d9ff;
}
