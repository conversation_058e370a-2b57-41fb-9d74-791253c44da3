import {
  HeaderConfig,
  SharedNavItem,
} from '@shared/components/app-header/app-header.component';
import { environment } from '../../environments/environment';

// Elder Wand specific navigation items
const elderWandNavItems: SharedNavItem[] = [
  {
    label: 'Launchpad',
    route: '/',
    selected: true,
    hasDropdown: false,
    icon: `svgs/dashboard.svg`,
  },
  {
    label: 'Build',
    route: '/agents',
    selected: false,
    hasDropdown: true,
    dropdownOpen: false,
    icon: `svgs/launch.svg`,
    dropdownItems: [
      {
        label: 'Create Agents',
        description: 'Build new agents',
        route: '/build/agents/individual',
        icon: `svgs/agents.svg`,
      },
      {
        label: 'Create Workflows',
        description: 'Build new Workflows',
        route: '/build/workflows/create',
        icon: `svgs/workflows.svg`,
      },
      {
        label: 'Create Tools',
        description: 'Create new Tools',
        route: '/libraries/tools/create',
        icon: `svgs/tools.svg`,
      },
      {
        label: 'Create Guardrails',
        description: 'Create new Guardrails',
        route: '/libraries/guardrails/create',
        icon: `svgs/guardrails.svg`,
      },
      {
        label: 'Create Knowledge Base',
        description: 'Create new Knowledge Base',
        route: '/libraries/knowledge-base/create',
        icon: `svgs/knowledgebase.svg`,
      },
    ],
  },
  {
    label: 'Marketplace',
    route: '/agent-list',
    selected: true,
    hasDropdown: false,
    icon: `svgs/marketplace.svg`,
  },
  {
    label: 'My Account',
    route: '/my-account',
    selected: false,
    hasDropdown: false,
    icon: `svgs/profile.svg`,
  },
];

// Available studio apps for the app drawer
const availableStudioApps = [
  {
    name: 'Product Studio',
    route: environment.productStudioUrl,
    icon: 'svgs/product-studio.svg',
    description: 'Product strategy and business model canvas',
  },
  {
    name: 'Experience Studio',
    route: environment.experienceStudioUrl,
    icon: 'svgs/experience-studio.svg',
    description: 'AI-powered design analysis and code generation',
  },
  {
    name: 'Console',
    route: environment.consoleUrl,
    icon: 'svgs/console.svg',
    description: 'Agent management and workflow automation',
  },
];

// Available languages for the language switcher
const availableLanguages = [
  { code: 'en', name: 'English' },
  { code: 'es', name: 'Español' },
  { code: 'fr', name: 'Français' },
];

// Elder Wand header configuration
export const elderWandHeaderConfig: HeaderConfig = {
  logoSrc: 'svgs/ascendion-short-logo',
  navItems: elderWandNavItems,
  showOrgSelector: true, // Elder Wand doesn't need org selector
  showThemeToggle: true,
  showAppDrawer: true,
  showProfileDropdown: true,
  showThemeToggleInProfile: true,
  showLanguageSwitcher: true,
  disableThemeToggle: true,
  disableLanguageChange: true,
  projectName: 'Elder Wand',
  redirectUrl: '/',
  currentApp: 'Elder Wand',
  availableApps: availableStudioApps,
  availableLanguages: availableLanguages,
  enableLogoAnimation: true,
  logoAnimationInterval: 4000, // 4 seconds between transitions
  logoAnimationStyle: 'fade',
  studioLogos: [
    'svgs/ascendion.svg',
    'svgs/aava-logo.svg',
    'svgs/launchpad-logo.svg',
  ],
  studioNames: [
    'Console Studio',
    'Experience Studio',
    'Product Studio',
    'Launchpad',
  ],
};
