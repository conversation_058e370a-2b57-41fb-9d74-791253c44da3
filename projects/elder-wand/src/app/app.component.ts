import { Component, OnInit, ViewChild, ViewContainerRef } from '@angular/core';
import { Router, RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';

import {
  SharedAppHeaderComponent,
  HeaderConfig,
} from '@shared/components/app-header/app-header.component';
import { CentralizedRedirectService } from '@shared/services/centralized-redirect.service';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { AuthTokenService } from '@shared/auth/services/auth-token.service';
import { AuthConfig } from '@shared/auth/interfaces/auth-config.interface';
import { AuthService } from '@shared/auth/services/auth.service';

import { ThemeService } from './shared/services/theme.service';
import { elderWandHeaderConfig } from './config/header.config';
import { environment } from '../environments/environment';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, CommonModule, SharedAppHeaderComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent implements OnInit {
  @ViewChild('dialogHost', { read: ViewContainerRef })
  dialogHost!: ViewContainerRef;
  headerConfig: HeaderConfig = elderWandHeaderConfig;
  showHeaderAndNav: boolean = true;
  showHeroSection: boolean = true;
  isLaunchpadRoute: boolean = false;
  currentYear = new Date().getFullYear();

  private routerSubscription: Subscription = new Subscription();

  constructor(
    private centralizedRedirectService: CentralizedRedirectService,
    private authTokenService: AuthTokenService,
    private tokenStorage: TokenStorageService,
    public themeService: ThemeService,
    private authService: AuthService,
    private router: Router,
  ) {}

  ngOnInit(): void {
    this.initializeTheme();
    this.configureAuthentication();
    if (!this.checkAuthenticationAndRedirect()) return;
    this.authTokenService.handleAuthCodeAndToken();
    this.authTokenService.startTokenCheck();
    this.routerSubscription = this.router.events.subscribe(() => {
      this.updateLayoutBasedOnRoute(this.router.url);
    });
    this.updateLayoutBasedOnRoute(this.router.url);
  }

  ngOnDestroy(): void {
    this.authTokenService.stopTokenCheck();
    this.routerSubscription?.unsubscribe();
  }

  onNavigation(route: string): void {
    console.log('Elder Wand Navigation to:', route);
  }

  onProfileAction(action: string): void {
    console.log('Profile action:', action);
  }

  onThemeToggle(theme: 'light' | 'dark'): void {
    this.themeService.setTheme(theme);
  }

  private initializeTheme(): void {
    const savedTheme = this.themeService.getCurrentTheme();
    this.themeService.setTheme(savedTheme);
  }

  private configureAuthentication(): void {
    const authConfig: AuthConfig = {
      apiAuthUrl: environment.elderWandApiAuthUrl,
      redirectUrl: environment.elderWandRedirectUrl,
      postLoginRedirectUrl: '/',
      appName: 'elder-wand',
    };
    this.authService.setAuthConfig(authConfig);
  }

  private checkAuthenticationAndRedirect(): boolean {
    const accessToken = this.tokenStorage.getAccessToken();
    const refreshToken = this.tokenStorage.getRefreshToken();
    if (!accessToken && !refreshToken) {
      this.centralizedRedirectService.storeIntendedDestination(
        window.location.href,
      );
      this.centralizedRedirectService.redirectToMarketingLogin();
      return false;
    }
    return true;
  }

  private updateLayoutBasedOnRoute(url: string): void {
    const hiddenRoutes = ['/login', '/marketplace', '/', ''];
    this.showHeaderAndNav = !hiddenRoutes.includes(url);
    this.showHeroSection =
      url !== '/my-agent-home' && !hiddenRoutes.includes(url);
    this.isLaunchpadRoute = url === '/dashboard' || url === '/';
  }
}
