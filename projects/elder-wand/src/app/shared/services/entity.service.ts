import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, map } from 'rxjs';
import { EntityResult } from '../interfaces/agent-list.interface';

export interface EntityResponse {
  message: string;
  results: EntityResult[];
  aggregations: any;
  totalResults: number;
  page: number;
  size: number;
  totalPages: number;
}

@Injectable({
  providedIn: 'root'
})
export class EntityService {
  private readonly baseUrl = 'https://aava-dev.avateam.io/search/v1/api';

  constructor(private http: HttpClient) {}

  /**
   * Get entities with pagination
   * @param page Page number (0-based)
   * @param size Number of items per page
   * @returns Observable of EntityResponse
   */
  getEntities(page: number = 0, size: number = 50): Observable<EntityResponse> {
    const params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    return this.http.get<EntityResponse>(`${this.baseUrl}/entities`, { params });
  }

  /**
   * Get only agent entities
   * @param page Page number (0-based)
   * @param size Number of items per page
   * @returns Observable of EntityResult[] containing only agents
   */
  getAgents(page: number = 0, size: number = 50): Observable<EntityResult[]> {
    return this.getEntities(page, size).pipe(
      map(response => response.results.filter(entity => entity.entityType === 'agent'))
    );
  }

  /**
   * Get agent by ID
   * @param id Agent ID
   * @returns Observable of EntityResult
   */
  getAgentById(id: number): Observable<EntityResult | undefined> {
    return this.getEntities().pipe(
      map(response => response.results.find(entity => entity.id === id && entity.entityType === 'agent'))
    );
  }
} 