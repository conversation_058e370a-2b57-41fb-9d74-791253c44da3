import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, map } from 'rxjs';
import { EntityResult } from '../interfaces/agent-list.interface';

export interface UnifiedSearchResponse {
  message: string;
  results: EntityResult[];
  aggregations: any;
  totalResults: number;
  page: number;
  size: number;
  totalPages: number;
}

export interface SearchParams {
  query: string;
  limit?: number;
  from?: number;
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface RevelioSearchParams {
  query: string;
  limit?: number;
  threshold?: number;
}

export interface RevelioSearchResponse {
  results: RevelioSearchResult[];
}

export interface RevelioSearchResult {
  id: string;
  content: string;
  score: number;
  metadata: {
    id: string;
    highlights: string;
    details: string;
    entityType: string;
    createdBy: string;
    description: string;
    name: string;
    deleted: boolean;
    modifiedBy: string;
    score: number;
  };
  matched_fields: string[];
}

@Injectable({
  providedIn: 'root',
})
export class SearchService {
  private readonly baseUrl = 'https://aava-dev.avateam.io/search/v1/api';
  private readonly revelioBaseUrl = 'https://revelio-search.azurewebsites.net';

  constructor(private http: HttpClient) {}

  /**
   * Unified search across all entities
   * @param params Search parameters
   * @returns Observable of UnifiedSearchResponse
   */
  unifiedSearch(params: SearchParams): Observable<UnifiedSearchResponse> {
    const httpParams = new HttpParams()
      .set('query', params.query)
      .set('limit', (params.limit || 10).toString())
      .set('from', (params.from || 0).toString())
      .set('sortField', params.sortField || '_score')
      .set('sortOrder', params.sortOrder || 'desc');

    return this.http.get<UnifiedSearchResponse>(`${this.baseUrl}/unified`, {
      params: httpParams,
    });
  }

  /**
   * Search agents by name, title, or description
   * @param query Search query
   * @param limit Number of results to return
   * @returns Observable of EntityResult[] containing only agents
   */
  searchAgents(query: string, limit: number = 200): Observable<EntityResult[]> {
    console.log('Search service: Searching for agents with query:', query);
    return this.unifiedSearch({ query, limit }).pipe(
      map((response) => {
        console.log('Search service: Full search response:', response);
        const agentResults = response.results.filter(
          (entity) => entity.entityType === 'agent',
        );
        console.log('Search service: Filtered agent results:', agentResults);
        return agentResults;
      }),
    );
  }

  /**
   * Revelio search using POST request
   * @param params Revelio search parameters
   * @returns Observable of RevelioSearchResponse
   */
  revelioSearch(
    params: RevelioSearchParams,
  ): Observable<RevelioSearchResponse> {
    console.log('Revelio search service: Searching with params:', params);
    const body = {
      query: params.query,
      limit: params.limit || 10,
      threshold: params.threshold || 0.1,
    };

    const headers = {
      accept: 'application/json',
      'Content-Type': 'application/json',
    };

    return this.http.post<RevelioSearchResponse>(
      `${this.revelioBaseUrl}/search`,
      body,
      { headers },
    );
  }
}
