import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly themeKey = 'user-theme'; // Key for localStorage
  private themeSubject = new BehaviorSubject<'light' | 'dark'>(this.loadInitialTheme());
  themeObservable = this.themeSubject.asObservable();

  constructor() {
    this.setTheme(this.themeSubject.value);
  }

  /** Apply a specific theme */
  setTheme(theme: 'light' | 'dark') {
    const body = document.body;
    body.classList.remove('light-theme', 'dark-theme');
    body.classList.add(`${theme}-theme`);

    localStorage.setItem(this.themeKey, theme); // Store theme preference
    this.themeSubject.next(theme); // Notify subscribers
  }

  /** Toggle between light and dark themes */
  toggleTheme() {
    const newTheme = this.getCurrentTheme() === 'dark' ? 'light' : 'dark';
    this.setTheme(newTheme);
  }

  /** Get the current applied theme */
  getCurrentTheme(): 'light' | 'dark' {
    return this.themeSubject.value;
  }

  /** Load the theme from localStorage when app starts */
  private loadInitialTheme(): 'light' | 'dark' {
    const savedTheme = localStorage.getItem(this.themeKey) as 'light' | 'dark';
    return savedTheme ? savedTheme : 'light';
  }
} 