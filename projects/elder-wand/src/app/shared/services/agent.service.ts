import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from 'projects/elder-wand/src/environments/environment';
import { Subject, takeUntil } from 'rxjs';

export interface SearchParams {
  page: number;
  records: number;
  status?: string;
  search?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AgentService {
  private baseUrl = environment.baseUrl;
  private cancelPreviousRequest$ = new Subject<void>();

  private http = inject(HttpClient); 

  getAllAgents(params: SearchParams, isDeleted: boolean = false) {
    // Cancel previous ongoing request
    this.cancelPreviousRequest$.next();
    
    let httpParams = new HttpParams()
      .set('page', params.page)
      .set('records', params.records)
      .set('isDeleted', isDeleted);
    
    if (params.search) {
      httpParams = httpParams.set('search', params.search);
    }

    if (params.status) {
      httpParams = httpParams.set('status', params.status);
    }
    const url = `${this.baseUrl}/agents/user`;

    return this.http.get(url, { params: httpParams }).pipe(
      takeUntil(this.cancelPreviousRequest$)
    );
  }

  getAgentMertrics() {
    const url = `${this.baseUrl}/agents/metrics`;
    return this.http.get(url)
  }

  public deleteCollaborativeAgent(agentId: string) {
    const url = `${this.baseUrl}/agents/${agentId}`;
    return this.http.delete(url);
  }
}
