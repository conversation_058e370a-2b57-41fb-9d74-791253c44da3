import { Injectable, inject } from '@angular/core';
import {
  CanActivate,
  Router,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AuthService } from '@shared/auth/services/auth.service';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';

@Injectable({
  providedIn: 'root',
})
export class MarketplaceAuthGuard implements CanActivate {
  private readonly authService = inject(AuthService);
  private readonly router = inject(Router);
  private readonly tokenStorage = inject(TokenStorageService);

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot,
  ): Observable<boolean | UrlTree> {
    const accessToken = this.tokenStorage.getAccessToken();
    const refreshToken = this.tokenStorage.getRefreshToken();

    // If we have a refresh token but no access token, try to refresh
    if (!accessToken && refreshToken) {
      return this.authService.refreshToken(refreshToken).pipe(
        map(() => {
          console.log('✅ Token refreshed successfully, allowing access');
          return true;
        }),
        catchError((error) => {
          console.log('❌ Token refresh failed, redirecting to marketplace');
          return of(this.router.createUrlTree(['/marketplace']));
        }),
      );
    }

    // If no tokens at all, redirect to marketplace
    if (!accessToken && !refreshToken) {
      console.log(
        '🏪 No authentication tokens found, redirecting to marketplace',
      );
      return of(this.router.createUrlTree(['/marketplace']));
    }

    // If we have an access token, allow access
    console.log('✅ User authenticated, allowing access to protected route');
    return of(true);
  }
}
