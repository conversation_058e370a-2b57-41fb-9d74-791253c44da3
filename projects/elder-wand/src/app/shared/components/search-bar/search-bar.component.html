<div id="search-bar-container">
  <div class="position-relative d-flex align-items-center search-wrapper">
    <div class="d-flex align-items-center justify-content-center search-icon">
      <ava-icon
        iconName="search"
        iconSize="20px"
        iconColor="var(--text-caption)"
        class="search-svg-icon"
      ></ava-icon>
    </div>

    <input
      type="text"
      class="search-input"
      aria-label="Search"
      [(ngModel)]="searchValue"
      (keydown.enter)="onSend()"
    />

    <div
      class="d-flex align-items-center animated-placeholder-wrapper"
      *ngIf="!searchValue"
    >
      <div class="animated-placeholder">
        <span
          class="d-flex align-items-center"
          *ngFor="let text of placeholderText"
          >{{ text }}</span
        >
      </div>
    </div>
    <!-- Send Button -->
    <button
      class="d-flex align-items-center justify-content-center send-button"
      (click)="onSend()"
    >
      <ava-icon
        iconName="send-horizontal"
        iconColor="black"
        class="send-icon"
      ></ava-icon>
    </button>
  </div>
</div>
