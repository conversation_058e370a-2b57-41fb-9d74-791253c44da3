@import "../../../../assets/themes/animate.scss";

#search-bar-container {
  .search-icon {
    position: absolute;
    left: 16px;
  }
  .search-input {
    width: 100%;
    padding: 1rem 4rem 1rem 3rem;
    border-radius: 1rem;
    background-color: var(--white);
    color: var(--text-caption);
    font-size: 1.5rem;
    font-weight: 400;
    line-height: normal;
    outline: none;
    border: double 1px transparent;
    background-image:
      linear-gradient(var(--white), var(--white)),
      linear-gradient(180deg, var(--ew-red-300) 0%, var(--ew-violet-300) 100%);
    background-origin: border-box;
    background-clip: padding-box, border-box;
    transition: all 0.3s ease;

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }

    &:focus {
      box-shadow: 0 0 0 3px rgba(240, 104, 150, 0.1);
    }
  }
  .animated-placeholder-wrapper {
    position: absolute;
    left: 48px;
    top: 50%;
    transform: translateY(-50%);
    overflow: hidden;
    height: 24px;
  }

  .animated-placeholder {
    height: 100%;
    animation: slide-up-down 10s ease-in-out infinite;
  }

  .animated-placeholder span {
    color: var(--text-caption);
    font-size: 20px;
    font-weight: 400;
    white-space: nowrap;
    height: 100%;
  }

  .send-button {
    position: absolute;
    right: 16px;
    background: none;
    border: none;
    cursor: pointer;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 3;

    &:hover:not(:disabled) {
      background-color: rgba(0, 0, 0, 0.1);
    }

    &:active:not(:disabled) {
      transform: scale(0.95);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    ::ng-deep .ava-icon-container {
      background: transparent;
      border: none;
      padding: 0;
      width: 24px; // Default size for 1920px screens
      height: 24px;

      lucide-icon {
        width: 24px !important;
        height: 24px !important;
      }
    }
  }
}
