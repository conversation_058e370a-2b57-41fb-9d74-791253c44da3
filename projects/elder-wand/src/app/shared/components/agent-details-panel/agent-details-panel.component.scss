@keyframes slideIn {
  from {
    transform: translateX(100%);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

:host {
  display: block;
  height: 100%;
  width: 100%;
}

.agent-details-panel {
  width: 100%;
  height: 100%;
  background-color: #fff;
  box-shadow: -4px 0 24px rgba(0, 0, 0, 0.1);
  animation: slideIn 0.3s ease;
  display: flex;
  flex-direction: column;
  position: relative;

  .details-header {
    padding: 0;
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;

    .close-btn {
      background: none;
      border: none;
      cursor: pointer;
      font-size: 24px;
      color: #666;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 50%;

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  /* =============================================================================
     SKELETON LOADER STYLES
     ============================================================================= */

  .skeleton-content {
    flex: 1;
    display: flex;
    flex-direction: column;

    .details-content {
      padding: 52px 32px 0 32px;
      display: flex;
      flex-direction: column;
      flex: 1;
      overflow-y: auto;
      min-height: 0;
    }
  }

  // Base skeleton element styles
  .skeleton-base {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px;
  }

  // Title skeleton
  .skeleton-title {
    @extend .skeleton-base;
    height: 38px;
    width: 85%;
    margin-bottom: 12px;
    border-radius: 6px;
  }

  // Main description skeleton
  .skeleton-description-main {
    @extend .skeleton-base;
    height: 24px;
    width: 100%;
    margin-top: 10px;
    margin-bottom: 12px;
  }

  // Tag skeletons
  .skeleton-tag {
    @extend .skeleton-base;
    height: 38px;
    width: 120px;
    border-radius: 24px;

    &:nth-child(1) {
      width: 110px;
    }
    &:nth-child(2) {
      width: 140px;
    }
    &:nth-child(3) {
      width: 130px;
    }
    &:nth-child(4) {
      width: 100px;
    }
  }

  // Metric skeletons
  .skeleton-metric-label {
    @extend .skeleton-base;
    height: 16px;
    width: 80%;
    margin: 0 auto 8px;
    border-radius: 3px;
  }

  .skeleton-metric-icon {
    @extend .skeleton-base;
    width: 25px;
    height: 25px;
    border-radius: 50%;
  }

  .skeleton-metric-value {
    @extend .skeleton-base;
    height: 19px;
    width: 70%;
    margin: 8px auto 0;
    border-radius: 3px;
  }

  // Section title skeleton
  .skeleton-section-title {
    @extend .skeleton-base;
    height: 28px;
    width: 60%;
    margin-bottom: 12px;
    border-radius: 5px;
  }

  // Description line skeletons
  .skeleton-description-line {
    @extend .skeleton-base;
    height: 16px;
    width: 100%;
    margin-bottom: 8px;

    &.short {
      width: 75%;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  // Button skeleton
  .skeleton-button {
    @extend .skeleton-base;
    height: 48px;
    width: 100%;
    border-radius: 8px;
  }

  // Special skeleton section styling
  .skeleton-section {
    background: linear-gradient(180deg, #f8f8f8 0%, #f0f0f0 100%);
    border: 1px solid #e5e7eb;

    .skeleton-section-title,
    .skeleton-description-line {
      background: linear-gradient(90deg, #e8e8e8 25%, #d8d8d8 50%, #e8e8e8 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
    }
  }

  // Loading State Styles
  .loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: 40px 32px;

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      text-align: center;

      .loading-spinner {
        animation: spin 1s linear infinite;
      }

      p {
        color: #616874;
        font-size: 16px;
        margin: 0;
      }
    }
  }

  // Error State Styles
  .error-state {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: 40px 32px;

    .error-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      text-align: center;
      max-width: 300px;

      p {
        color: #f74646;
        font-size: 16px;
        margin: 0;
        line-height: 1.5;
      }
    }
  }

  .details-content {
    padding: 52px 32px 0 32px;
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: auto;
    min-height: 0;

    .upper-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 30px;
      padding-bottom: 30px;
    }

    .add-to-list button,
    .add-to-list awe-button,
    .add-to-list awe-button button {
      min-width: 0 !important;
    }

    .agent-tags {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      margin-top: 1rem;
      gap: 12px;
      padding: 0;
      width: auto;
      justify-content: flex-start;

      .tag {
        display: flex;
        align-items: center;
        text-align: center;
        color: #23262f;

        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        padding: 12px 24px;
        border-radius: 24px;
        height: 38px;
        background-color: #ededf3;
        max-width: 170px;
      }
    }

    .details-title {
      h2 {
        color: #4c515b;

        font-size: 32px;
        font-style: normal;
        font-weight: 700;
        line-height: 38.4px;
        margin-bottom: 12px;
        margin-top: 0;
        // Title truncation - 1 line only
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .details-description {
      color: #6b7280;

      font-size: 20px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      margin-bottom: 8px;
      text-align: left;
      margin-top: 0;
      margin-top: 10px;
      margin-bottom: 12px;
      // Description truncation - 2 lines only
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
    }

    .details-metrics {
      display: flex;
      justify-content: space-between;
      align-items: stretch;
      margin-bottom: 0px;
      padding: 0;
      gap: 16px;

      .metric {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        min-height: 120px;

        .label-1 {
          color: #858aad;
          text-align: center;

          font-size: 16px;
          font-style: normal;
          font-weight: 800;
          line-height: 110%;
          margin-bottom: 8px;
        }

        .label {
          color: #666d99;

          font-size: 19px;
          font-style: normal;
          font-weight: 600;
          line-height: 97%;
          margin-top: 8px;
        }

        .middle-content {
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 8px 0;
          min-height: 25px;
        }

        .score {
          color: #616874;

          font-size: 20px;
          font-style: normal;
          font-weight: 700;
          line-height: 150%;
        }

        .rating-value {
          color: #616874;

          font-size: 20px;
          font-style: normal;
          font-weight: 700;
          line-height: 150%;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 4px;
        }

        ava-icon {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .details-section {
      margin-bottom: 15px;
      border-radius: 12px;
      background: linear-gradient(180deg, #fde9ef 0%, #f0ebf8 100%);
      padding: 24px;
      height: 200px;
      overflow: hidden;
      transition: all 0.3s ease;

      &:last-child {
        margin-bottom: 0;
      }

      &.expanded {
        overflow-y: auto;
      }

      h3 {
        color: #4c515b;

        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: 28.8px;
        text-align: left;
        margin-bottom: 12px;
        margin-top: 0;
      }

      .description-container {
        position: relative;
        height: calc(100% - 40px); // Account for h3 height
        display: flex;
        flex-direction: column;
      }

      p {
        color: #616874;

        font-size: 1rem;
        font-style: normal;
        font-weight: 400;
        line-height: 150%;
        text-align: left;
        margin: 0;
        flex: 1;

        // Line clamp to 4 lines when not expanded
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
        transition: all 0.3s ease;

        &.expanded {
          -webkit-line-clamp: unset;
          overflow: visible;
          text-overflow: unset;
        }
      }

      .read-more-btn {
        background: none;
        border: none;
        color: #997bcf;

        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        padding: 8px 0;
        margin-top: 8px;
        text-align: left;
        transition: color 0.2s ease;

        &:hover {
          color: #7b5ba8;
        }

        &:focus {
          outline: none;
          color: #7b5ba8;
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-top: 16px;

        .info-item {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .info-label {
            color: #858aad;

            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: 110%;
          }

          .info-value {
            color: #4c515b;

            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 120%;
            word-break: break-word;
          }
        }
      }

      .type-badge {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 0.875;

        .icon {
          font-size: 1rem;
        }
      }
    }
  }

  .action-button {
    width: 100%;
    box-sizing: border-box;
    margin-top: auto;
    flex-shrink: 0;
  }
}

.agent-divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(180deg, #f06896 0%, #997bcf 100%);
  border: none;
  margin: 32px 0;
  border-radius: 1px;
}

// Remove right margin from awe-button globally in this component
:host ::ng-deep awe-button {
  margin-top: 0 !important;
  padding: 0 !important;
}

// Enhanced responsive adjustments - aggressive scaling down from 1920px baseline
@media (max-width: 1600px) {
  .agent-details-panel {
    .details-content {
      padding: 36px 24px 0 24px; // Significantly reduced from 52px 32px default

      .upper-content {
        gap: 20px; // Significantly reduced from 30px default
        padding-bottom: 20px; // Significantly reduced from 30px default
      }

      .details-title h2 {
        font-size: 24px; // Significantly reduced from 32px default
        line-height: 28px; // Significantly reduced from 38.4px default
        margin-bottom: 8px; // Reduced from 12px default
      }

      .details-description {
        font-size: 16px; // Significantly reduced from 20px default
        line-height: 19px; // Significantly reduced from 24px default
        margin-bottom: 8px; // Reduced from 12px default
      }

      .agent-tags {
        gap: 8px; // Sign/ Significantly reduced from 32px default

        .tag {
          font-size: 12px; // Significantly reduced from 16px default
          line-height: 16px; // Significantly reduced from 24px default
          padding: 8px 16px; // Significantly reduced from 12px 24px default
          height: 28px; // Significantly reduced from 38px default
        }
      }

      .details-metrics {
        gap: 12px;
        min-height: 100px;

        .metric {
          min-height: 100px;

          .label-1 {
            font-size: 12px; // Significantly reduced from 16px default
            margin-bottom: 6px;
          }

          .label {
            font-size: 15px; // Significantly reduced from 19px default
            margin-top: 6px;
          }

          .middle-content {
            margin: 6px 0;
            min-height: 20px;
          }

          .score {
            font-size: 16px; // Significantly reduced from 20px default
          }

          .rating-value {
            font-size: 16px; // Significantly reduced from 20px default
          }

          ava-icon {
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      .details-section {
        padding: 16px; // Significantly reduced from 24px default
        height: 160px; // Reduced height for smaller screens

        h3 {
          font-size: 18px; // Significantly reduced from 24px default
          line-height: 21px; // Significantly reduced from 28.8px default
          margin-bottom: 8px; // Reduced from 12px default
        }

        p {
          font-size: 0.8rem; // Significantly reduced from 1rem default
        }
      }
    }
  }

  .agent-divider {
    margin: 24px 0; // Significantly reduced from 32px default
  }
}

// Specific breakpoint for 1440px screens
@media (max-width: 1440px) {
  .agent-details-panel {
    .details-content {
      padding: 46px 28px 0 28px;

      .upper-content {
        gap: 26px;
        padding-bottom: 26px;
      }

      .details-title h2 {
        font-size: 28px;
        line-height: 34px;
        margin-bottom: 10px;
      }

      .details-description {
        font-size: 18px;
        line-height: 22px;
        margin-bottom: 10px;
      }

      .agent-tags {
        gap: 10px;

        .tag {
          font-size: 14px;
          line-height: 20px;
          padding: 10px 20px;
          height: 34px;
        }
      }

      .details-metrics {
        gap: 14px;
        min-height: 110px;

        .metric {
          min-height: 110px;

          .label-1 {
            font-size: 14px;
            margin-bottom: 7px;
          }

          .label {
            font-size: 17px;
            margin-top: 7px;
          }

          .middle-content {
            margin: 7px 0;
            min-height: 22px;
          }

          .score {
            font-size: 18px;
          }

          .rating-value {
            font-size: 18px;
          }

          ava-icon {
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      .details-section {
        padding: 20px;
        height: 180px; // Adjusted height for 1440px screens

        h3 {
          font-size: 20px;
          line-height: 24px;
          margin-bottom: 10px;
        }

        p {
          font-size: 0.9rem;
        }
      }
    }
  }

  .agent-divider {
    margin: 28px 0;
  }
}

@media (max-width: 1400px) {
  .agent-details-panel {
    .details-content {
      padding: 44px 26px 0 26px;

      .upper-content {
        gap: 24px;
        padding-bottom: 24px;
      }

      .details-title h2 {
        font-size: 26px;
        line-height: 32px;
        margin-bottom: 10px;
      }

      .details-description {
        font-size: 18px;
        line-height: 22px;
        margin-bottom: 10px;
      }

      .agent-tags {
        gap: 10px; // margin: 0 0 26px 0;

        .tag {
          font-size: 14px;
          line-height: 20px;
          padding: 10px 20px;
          height: 34px;
        }
      }

      .details-metrics {
        gap: 14px;
        min-height: 110px;

        .metric {
          min-height: 110px;

          .label-1 {
            font-size: 14px;
            margin-bottom: 7px;
          }

          .label {
            font-size: 17px;
            margin-top: 7px;
          }

          .middle-content {
            margin: 7px 0;
            min-height: 22px;
          }

          .score {
            font-size: 18px;
          }

          .rating-value {
            font-size: 18px;
          }

          ava-icon {
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      .details-section {
        padding: 20px;
        height: 180px; // Adjusted height for 1400px screens

        h3 {
          font-size: 20px;
          line-height: 24px;
          margin-bottom: 10px;
        }

        p {
          font-size: 0.9rem;
        }
      }
    }
  }

  .agent-divider {
    margin: 26px 0;
  }
}

@media (max-width: 1200px) {
  .agent-details-panel {
    .details-content {
      padding: 42px 24px 0 24px;

      .upper-content {
        gap: 22px;
        padding-bottom: 22px;
      }

      .details-title h2 {
        font-size: 24px;
        line-height: 30px;
        margin-bottom: 9px;
      }

      .details-description {
        font-size: 17px;
        line-height: 21px;
        margin-bottom: 9px;
      }

      .agent-tags {
        gap: 9px;

        .tag {
          font-size: 13px;
          line-height: 18px;
          padding: 9px 18px;
          height: 32px;
        }
      }

      .details-metrics {
        gap: 12px;
        min-height: 100px;

        .metric {
          min-height: 100px;

          .label-1 {
            font-size: 13px;
            margin-bottom: 6px;
          }

          .label {
            font-size: 16px;
            margin-top: 6px;
          }

          .middle-content {
            margin: 6px 0;
            min-height: 20px;
          }

          .score {
            font-size: 17px;
          }

          .rating-value {
            font-size: 17px;
          }

          ava-icon {
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      .details-section {
        padding: 18px;
        height: 170px; // Adjusted height for 1200px screens

        h3 {
          font-size: 18px;
          line-height: 22px;
          margin-bottom: 9px;
        }

        p {
          font-size: 0.85rem;
        }
      }
    }
  }

  .agent-divider {
    margin: 24px 0;
  }
}

@media (max-width: 1024px) {
  .agent-details-panel {
    .details-content {
      padding: 40px 22px 0 22px;

      .upper-content {
        gap: 20px;
        padding-bottom: 20px;
      }

      .details-title h2 {
        font-size: 22px;
        line-height: 28px;
        margin-bottom: 8px;
      }

      .details-description {
        font-size: 16px;
        line-height: 20px;
        margin-bottom: 8px;
      }

      .agent-tags {
        gap: 8px;

        .tag {
          font-size: 12px;
          line-height: 16px;
          padding: 8px 16px;
          height: 30px;
        }
      }

      .details-metrics {
        gap: 10px;
        min-height: 90px;

        .metric {
          min-height: 90px;

          .label-1 {
            font-size: 12px;
            margin-bottom: 5px;
          }

          .label {
            font-size: 15px;
            margin-top: 5px;
          }

          .score {
            font-size: 16px;
            margin: 5px 0;
          }

          .rating-value {
            font-size: 16px;
            margin: 5px 0;
          }

          ava-icon {
            margin: 5px 0;
          }
        }
      }

      .details-section {
        padding: 16px;
        height: 160px; // Adjusted height for 1024px screens

        h3 {
          font-size: 16px;
          line-height: 20px;
          margin-bottom: 8px;
        }

        p {
          font-size: 0.8rem;
        }
      }
    }
  }

  .agent-divider {
    margin: 22px 0;
  }
}

@media (max-width: 768px) {
  .agent-details-panel {
    .details-content {
      padding: 36px 18px 0 18px;

      .upper-content {
        gap: 18px;
        padding-bottom: 18px;
      }

      .details-title h2 {
        font-size: 20px;
        line-height: 26px;
        margin-bottom: 7px;
      }

      .details-description {
        font-size: 15px;
        line-height: 19px;
        margin-bottom: 7px;
      }

      .add-to-list {
        height: 22px;
        padding: 0 11px;
        font-size: 11px;
        margin: 0 0 20px 0;
      }

      .agent-tags {
        gap: 7px;

        .tag {
          font-size: 11px;
          line-height: 14px;
          padding: 7px 14px;
          height: 28px;
        }
      }

      .details-metrics {
        gap: 8px;
        min-height: 80px;

        .metric {
          min-height: 80px;

          .label-1 {
            font-size: 11px;
            margin-bottom: 4px;
          }

          .label {
            font-size: 14px;
            margin-top: 4px;
          }

          .score {
            font-size: 15px;
            margin: 4px 0;
          }

          .rating-value {
            font-size: 15px;
            margin: 4px 0;
          }

          ava-icon {
            margin: 4px 0;
          }
        }
      }

      .details-section {
        padding: 14px;
        height: 120px; // Adjusted height for 768px screens

        h3 {
          font-size: 14px;
          line-height: 18px;
          margin-bottom: 7px;
        }

        p {
          font-size: 0.75rem;
        }
      }
    }
  }

  .agent-divider {
    margin: 20px 0;
  }
}

@media (max-width: 480px) {
  .agent-details-panel {
    .details-content {
      padding: 32px 16px 0 16px;

      .upper-content {
        gap: 16px;
        padding-bottom: 16px;
      }

      .details-title h2 {
        font-size: 18px;
        line-height: 24px;
        margin-bottom: 6px;
      }

      .details-description {
        font-size: 14px;
        line-height: 18px;
        margin-bottom: 6px;
      }

      .add-to-list {
        height: 20px;
        padding: 0 10px;
        font-size: 10px;
        margin: 0 0 18px 0;
      }

      .agent-tags {
        gap: 6px;

        .tag {
          font-size: 10px;
          line-height: 12px;
          padding: 6px 12px;
          height: 26px;
        }
      }

      .details-metrics {
        gap: 6px;
        min-height: 70px;

        .metric {
          min-height: 70px;

          .label-1 {
            font-size: 10px;
            margin-bottom: 3px;
          }

          .label {
            font-size: 13px;
            margin-top: 3px;
          }

          .score {
            font-size: 14px;
            margin: 3px 0;
          }

          .rating-value {
            font-size: 14px;
            margin: 3px 0;
          }

          ava-icon {
            margin: 3px 0;
          }
        }
      }

      .details-section {
        padding: 12px;

        h3 {
          font-size: 12px;
          line-height: 16px;
          margin-bottom: 6px;
        }

        p {
          font-size: 0.7rem;
        }
      }
    }
  }

  .agent-divider {
    margin: 18px 0;
  }
}

// Ultra-wide screen support - LARGER content for larger screens (like launchpad)
@media (min-width: 1920px) {
  .agent-details-panel {
    max-width: 1400px; // Constrain content width like launchpad
    margin: 0 auto;

    .details-content {
      padding: 64px 40px 0 40px;

      .upper-content {
        gap: 36px;
        padding-bottom: 36px;
      }

      .details-title h2 {
        font-size: 40px; // LARGER than default 32px
        line-height: 48px;
        margin-bottom: 15px;
      }

      .details-description {
        font-size: 24px; // LARGER than default 20px
        line-height: 28px;
        margin-bottom: 15px;
      }

      .add-to-list {
        height: 40px; // LARGER than default 32px
        padding: 0 20px;
        font-size: 18px; // LARGER than default 16px
        margin: 0 0 40px 0;
      }

      .agent-tags {
        gap: 15px; // LARGER than default 12px

        .tag {
          font-size: 18px; // LARGER than default 16px
          line-height: 28px;
          padding: 15px 30px;
          height: 46px; // LARGER than default 38px
        }
      }

      .details-metrics {
        .metric {
          .label-1 {
            font-size: 18px; // LARGER than default 16px
          }

          .label {
            font-size: 22px; // LARGER than default 19px
          }

          .score {
            font-size: 24px; // LARGER than default 20px
          }

          .rating {
            font-size: 24px; // LARGER than default 20px
          }
        }
      }

      .details-section {
        padding: 32px; // LARGER than default 24px

        h3 {
          font-size: 28px; // LARGER than default 24px
          line-height: 34px;
          margin-bottom: 15px;
        }

        p {
          font-size: 1.2rem; // LARGER than default 1rem
        }
      }
    }
  }

  .agent-divider {
    margin: 40px 0; // LARGER than default 32px
  }
}

// 4K screen support - EVEN LARGER content for largest screens (like launchpad)
@media (min-width: 2560px) {
  .agent-details-panel {
    max-width: 1800px; // Even larger content width like launchpad
    margin: 0 auto;

    .details-content {
      padding: 80px 48px 0 48px;

      .upper-content {
        gap: 48px;
        padding-bottom: 48px;
      }

      .details-title h2 {
        font-size: 48px; // EVEN LARGER for 4K
        line-height: 56px;
        margin-bottom: 18px;
      }

      .details-description {
        font-size: 28px; // EVEN LARGER for 4K (like launchpad 22px)
        line-height: 32px;
        margin-bottom: 18px;
      }

      .add-to-list {
        height: 48px; // EVEN LARGER for 4K
        padding: 0 24px;
        font-size: 20px; // EVEN LARGER for 4K
        margin: 0 0 48px 0;
      }

      .agent-tags {
        gap: 18px; // EVEN LARGER for 4K

        .tag {
          font-size: 20px; // EVEN LARGER for 4K
          line-height: 32px;
          padding: 18px 36px;
          height: 54px; // EVEN LARGER for 4K
        }
      }

      .details-metrics {
        .metric {
          .label-1 {
            font-size: 20px; // EVEN LARGER for 4K
          }

          .label {
            font-size: 26px; // EVEN LARGER for 4K
          }

          .score {
            font-size: 28px; // EVEN LARGER for 4K
          }

          .rating {
            font-size: 28px; // EVEN LARGER for 4K
          }
        }
      }

      .details-section {
        padding: 40px; // EVEN LARGER for 4K

        h3 {
          font-size: 32px; // EVEN LARGER for 4K
          line-height: 40px;
          margin-bottom: 18px;
        }

        p {
          font-size: 1.4rem; // EVEN LARGER for 4K
        }
      }
    }
  }

  .agent-divider {
    margin: 48px 0; // EVEN LARGER for 4K
  }
}
