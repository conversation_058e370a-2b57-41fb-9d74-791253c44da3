<div class="agent-details-panel" *ngIf="agent || showSkeleton">
  <div class="details-header">
    <button class="close-btn" (click)="onClose()" [disabled]="showSkeleton">
      <ava-icon iconName="x" iconSize="36px" iconColor="#52577A"></ava-icon>
    </button>
  </div>

  <!-- Skeleton Loader -->
  <div *ngIf="shouldShowSkeleton" class="skeleton-content">
    <div class="details-content">
      <div class="upper-content">
        <div>
          <!-- Skeleton Title -->
          <div class="details-title">
            <div class="skeleton-title"></div>
          </div>

          <!-- Skeleton Description -->
          <div class="skeleton-description-main"></div>

          <!-- Skeleton Tags Section -->
          <div class="agent-tags">
            <div class="skeleton-tag"></div>
            <div class="skeleton-tag"></div>
            <div class="skeleton-tag"></div>
            <div class="skeleton-tag"></div>
          </div>

          <!-- Skeleton Divider -->
          <div class="agent-divider"></div>

          <!-- Skeleton Metrics -->
          <div class="details-metrics">
            <div class="metric" *ngFor="let item of [1, 2, 3, 4]">
              <div class="skeleton-metric-label"></div>
              <div class="middle-content">
                <div class="skeleton-metric-icon"></div>
              </div>
              <div class="skeleton-metric-value"></div>
            </div>
          </div>

          <div class="agent-divider"></div>
        </div>

        <div>
          <!-- Skeleton Details Section -->
          <div class="details-section skeleton-section">
            <div class="skeleton-section-title"></div>
            <div class="description-container">
              <div class="skeleton-description-line"></div>
              <div class="skeleton-description-line"></div>
              <div class="skeleton-description-line"></div>
              <div class="skeleton-description-line short"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Skeleton Action Button -->
      <div class="action-button mb-3">
        <div class="skeleton-button"></div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !shouldShowSkeleton" class="error-state">
    <div class="error-content">
      <ava-icon
        iconName="alert-circle"
        iconSize="32px"
        iconColor="#f74646"
      ></ava-icon>
      <p>{{ error }}</p>
      <ava-button
        label="Retry"
        variant="secondary"
        size="small"
        (userClick)="fetchAgentDetails()"
      ></ava-button>
    </div>
  </div>

  <!-- Actual Content (shown when not loading and no error) -->
  <div class="details-content" *ngIf="shouldShowContent">
    <div class="upper-content">
      <div>
        <div class="details-title">
          <h2>{{ getAgentName() }}</h2>
        </div>

        <p class="details-description">
          {{ getAgentDescription() }}
        </p>

        <!-- Tags Section -->
        <div class="agent-tags">
          <span class="tag">#1 in Agents</span>
          <span class="tag">Code Migration</span>
          <span class="tag">Development</span>
          <span class="tag">Backend</span>
        </div>

        <!-- Orange Divider -->
        <div class="agent-divider"></div>

        <div class="details-metrics">
          <div class="metric">
            <span class="label-1">Category</span>
            <div class="middle-content">
              <ava-icon
                iconName="code"
                iconSize="25px"
                iconColor="#616874"
                class="code-icon"
              ></ava-icon>
            </div>
            <span class="label">{{
              agent.entityData?.entityType || "agent"
            }}</span>
          </div>

          <div class="metric">
            <span class="label-1">Developed</span>
            <div class="middle-content">
              <ava-icon
                iconName="user"
                iconSize="25px"
                iconColor="#616874"
                class="profile-svg-icon"
              ></ava-icon>
            </div>
            <span class="label">{{ getCreatedBy() }}</span>
          </div>

          <div class="metric">
            <span class="label-1">Relevancy</span>
            <div class="middle-content">
              <div class="score">{{ getAgentScore() }}/10</div>
            </div>
            <span class="label">Score</span>
          </div>

          <div class="metric">
            <span class="label-1">Agent</span>
            <div class="middle-content">
              <div class="rating-value">
                <span class="score">{{ agent.rating || 4.5 }}</span>
                <ava-icon
                  iconName="star"
                  iconSize="16px"
                  iconColor="#616874"
                  class="agent-star-icon"
                ></ava-icon>
              </div>
            </div>
            <span class="label">Rating</span>
          </div>
        </div>

        <div class="agent-divider"></div>
      </div>

      <div>
        <div class="details-section" [class.expanded]="isDescriptionExpanded">
          <h3>What it's for</h3>
          <div class="description-container">
            <p [class.expanded]="isDescriptionExpanded">
              {{ getAgentDescription() }}
            </p>
            <button
              *ngIf="getAgentDescription().length > 200"
              class="read-more-btn"
              (click)="toggleDescription()"
              type="button"
            >
              {{ isDescriptionExpanded ? "Read Less" : "Read More" }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="action-button mb-3">
      <ava-button
        label="Go to Playground"
        variant="primary"
        size="large"
        width="100%"
        (userClick)="onGoToPlayground()"
        [pill]="false"
        [disabled]="!agent.entityData"
      ></ava-button>
    </div>
  </div>
</div>
