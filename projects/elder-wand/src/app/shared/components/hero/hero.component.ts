import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GlobalStoreService } from '../../service/global-store.service';
import { IconComponent } from '@ava/play-comp-library';
import { TokenStorageService } from '@shared/index';
import { HeroContent } from '../../interfaces/launchpad.interface';
import { HERO_TEXT_LOOKUP } from '../../constants/constant';
import { SearchBar } from '../search-bar/search-bar.component';

@Component({
  selector: 'app-hero',
  templateUrl: './hero.component.html',
  styleUrls: ['./hero.component.scss'],
  standalone: true,
  imports: [CommonModule, SearchBar, IconComponent],
})
export class Hero implements OnInit {
  @Input() placeholderText: string[] = [];
  @Input() heroType?: string;
  @Output() searchQueryChange = new EventEmitter<string>();
  @Output() sendClicked = new EventEmitter<string>();
  heroContent: HeroContent = HERO_TEXT_LOOKUP['Sales'];
  selectedUser: any;

  constructor(
    private readonly globalStoreService: GlobalStoreService,
    private readonly tokenStorageService: TokenStorageService,
  ) {}

  ngOnInit() {
    if (this.heroType) {
      this.heroContent = HERO_TEXT_LOOKUP[this.heroType] || this.heroContent;
    } else {
      this.globalStoreService.selectedUser.subscribe((user) => {
        this.selectedUser = user;
        if (user && user.type) {
          this.heroContent = HERO_TEXT_LOOKUP[user.type] || this.heroContent;
        }
      });
    }
  }

  get userName(): string {
    const userName = this.tokenStorageService.getDaName() || 'User';
    return userName.split(' ')[0];
  }

  /**
   * Handle search query change from search bar
   */
  onSearchQueryChange(query: string): void {
    this.searchQueryChange.emit(query);
  }

  /**
   * Handle send clicked from search bar
   */
  onSendClicked(query: string): void {
    this.sendClicked.emit(query);
  }
}
