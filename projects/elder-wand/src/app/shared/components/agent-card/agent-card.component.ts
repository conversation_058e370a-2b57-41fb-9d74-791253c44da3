import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { IconComponent } from '@ava/play-comp-library';

export interface Agent {
  id: string;
  title: string;
  description: string;
  rating?: number;
  users?: number;
  status?: 'approved' | 'pending' | 'denied' | 'draft';
  createdDate?: string;
  author?: string;
  timeAgo?: string;
}

export interface AgentCardAction {
  type: 'view' | 'edit' | 'delete' | 'play' | 'details';
  agent: Agent;
}

@Component({
  selector: 'app-agent-card',
  imports: [CommonModule, IconComponent],
  standalone: true,
  templateUrl: './agent-card.component.html',
  styleUrls: ['./agent-card.component.scss'],
})
export class AgentCardComponent {
  @Input() agent: Agent | null = null;
  @Input() isLoading: boolean = false;
  @Input() variant: 'marketplace' | 'dashboard' = 'marketplace';
  @Input() showTwoColumns: boolean = false;

  @Output() cardClick = new EventEmitter<Agent>();
  @Output() actionClick = new EventEmitter<AgentCardAction>();

  onCardClick(): void {
    if (this.agent && !this.isLoading) {
      this.cardClick.emit(this.agent);
    }
  }

  onActionClick(event: Event, actionType: AgentCardAction['type']): void {
    event.stopPropagation();
    if (this.agent) {
      this.actionClick.emit({
        type: actionType,
        agent: this.agent,
      });
    }
  }

  getStatusClass(status?: string): string {
    return `status-${status?.toLowerCase() || 'unknown'}`;
  }

  truncateText(text: string, maxLength: number = 75): string {
    if (!text) return '';
    return text.length > maxLength
      ? text.substring(0, maxLength) + '...'
      : text;
  }
}
