import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

interface AnalyticsData {
  agentsUsedToday: {
    count: number;
    percentage: string;
    chartData: any[];
  };
  taskAchieved: {
    percentage: number;
    categories: {
      backend: number;
      frontend: number;
      creation: number;
      testing: number;
    };
  };
  timeSaved: {
    minutes: number;
    agentsUsed: number;
    chartData: any[];
  };
}

@Component({
  selector: 'app-analytics-card',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './analytics-card.component.html',
  styleUrls: ['./analytics-card.component.scss']
})
export class AnalyticsCardComponent implements OnInit {
  
  analyticsData: AnalyticsData = {
    agentsUsedToday: {
      count: 12,
      percentage: '20%',
      chartData: [
        { date: new Date('2024-01-01'), value: 8 },
        { date: new Date('2024-01-02'), value: 12 },
        { date: new Date('2024-01-03'), value: 6 },
        { date: new Date('2024-01-04'), value: 15 },
        { date: new Date('2024-01-05'), value: 10 },
        { date: new Date('2024-01-06'), value: 18 },
        { date: new Date('2024-01-07'), value: 12 }
      ]
    },
    taskAchieved: {
      percentage: 98,
      categories: {
        backend: 35,
        frontend: 25,
        creation: 25,
        testing: 15
      }
    },
    timeSaved: {
      minutes: 200,
      agentsUsed: 285,
      chartData: [
        { category: 'Mon', value: 45 },
        { category: 'Tue', value: 30 },
        { category: 'Wed', value: 55 },
        { category: 'Thu', value: 25 },
        { category: 'Fri', value: 40 },
        { category: 'Sat', value: 35 },
        { category: 'Sun', value: 50 }
      ]
    }
  };

  ngOnInit() {
    // Component initialization
  }

  getTaskAchievedCategories() {
    return [
      { name: 'Backend', value: this.analyticsData.taskAchieved.categories.backend, color: '#8B5CF6' },
      { name: 'Frontend', value: this.analyticsData.taskAchieved.categories.frontend, color: '#EC4899' },
      { name: 'Creation', value: this.analyticsData.taskAchieved.categories.creation, color: '#F97316' },
      { name: 'Testing', value: this.analyticsData.taskAchieved.categories.testing, color: '#10B981' }
    ];
  }
}
