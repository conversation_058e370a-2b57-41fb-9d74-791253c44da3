import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';

@Component({
  selector: 'app-documents',
  imports: [CommonModule],
  templateUrl: './documents.component.html',
  styleUrl: './documents.component.scss'
})
export class DocumentsComponent {
  docCards = [
    {
      title: 'RESTful API',
      description: 'Our API follows REST principles with intuitive endpoints and consistent JSON responses.',
      features: [
        'Simple authentic with API Keys',
        'Comprehensive error handling',
        'Rate limiting with generous quotes',
        'HTTPS encryption for all requests'
      ],
      footer: {
        type: 'button',
        text: 'View API Documents'
      }
    },
    {
      title: 'Getting Started',
      description: 'Integrate our API in minutes with these simple steps:',
      steps: [
        'Sign up for an API Key',
        'Install our client Library',
        'Make your first API call'
      ],
      footer: {
        type: 'code',
        code: 'Curl -H "Authorization: Bearr YOUR_API_KEY" https://api.example.com/v1/date'
      }
    }
  ];
}
