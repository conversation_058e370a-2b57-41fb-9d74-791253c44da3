<div class="documents-page">
  <h1 class="page-title">Documents</h1>
  <div class="row g-3">
    <div class="col-12 col-md-6" *ngFor="let card of docCards">
      <div class="document-outer-card">
        <div class="document-card">
          <h3>{{ card.title }}</h3>
          <p>{{ card.description }}</p>
          <ul *ngIf="card.features">
            <li *ngFor="let feature of card.features">{{ feature }}</li>
          </ul>
          <ol *ngIf="card.steps">
            <li *ngFor="let step of card.steps">{{ step }}</li>
          </ol>
          <div class="document-footer">
            <button *ngIf="card.footer.type === 'button'">{{ card.footer.text }}</button>
            <code class="code-block" *ngIf="card.footer.type === 'code'">{{ card.footer.code }}</code>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
