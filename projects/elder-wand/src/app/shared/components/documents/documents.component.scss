.documents-page {
  padding-left: 32px;
  padding-right: 32px;
  .page-title {
    text-align: center;
    margin: 0 0 24px 0;
    color: #14161f;
    text-align: center;

    font-size: 48px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    letter-spacing: -0.912px;
  }

  .document-cards-row {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
    margin-bottom: 140px;
  }

  .document-outer-card {
    padding: 16px 0;
    height: 296px;
    border: 1px solid #dadce7;
    border-radius: 24px;
    background: #fff;
    display: flex;
    align-items: stretch;
    justify-content: center;
    width: 100%;
    // max-width: 876px;
    box-sizing: border-box;
    margin-bottom: 1rem;

    @media (max-width: 900px) {
      height: auto;
      max-width: 100%;
      padding: 1rem 0.5rem;
    }

    @media (max-width: 600px) {
      padding: 0.5rem 0.25rem;
      border-radius: 12px;
    }

    .document-card {
      width: 100%;
      padding: 16px;
      display: flex;
      flex-direction: column;
      background: transparent;
      border-radius: 16px;
      border: none;
      height: 100%;

      h3 {
        color: #292c3d;
        font-size: 20px;
        font-style: normal;
        font-weight: 500;
        line-height: 140%;
        margin: 0 0 8px 0;
        flex-shrink: 0; // Fixed header
      }

      p {
        color: #33364d;

        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 150%;
        letter-spacing: -0.304px;
        text-align: left;
        margin: 0 0 8px 0;
        flex-shrink: 0; // Fixed content
      }

      ul,
      ol {
        color: #666d99;

        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 150%;
        letter-spacing: -0.304px;
        padding-left: 1rem;
        margin: 0 0 8px 0;
        flex-shrink: 0; // Fixed content
      }

      .document-footer {
        margin-top: auto; // Push footer to bottom
        display: flex;
        align-items: center;
        flex-shrink: 0; // Static footer
      }

      button {
        background: #fff;
        border: 2px solid transparent;
        background:
          linear-gradient(#fff, #fff) padding-box,
          linear-gradient(
              87deg,
              rgba(250, 167, 74, 1) 8.06%,
              rgba(67, 131, 230, 1) 73.25%
            )
            border-box;
        border-radius: 6px;
        color: #14161f;
        text-align: center;

        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 150%;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 0.5rem 1rem;
      }

      .code-block {
        color: #292c3d;
        text-align: left;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%;
        background-color: #ededf3;
        padding: 8px 12px;
        border-radius: 4px;
        width: 100%;
        box-sizing: border-box;
      }
    }
  }
}
