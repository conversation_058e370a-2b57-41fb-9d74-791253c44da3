@use "../../../../assets/themes/mixins" as mixins;

#filter-tab-container {
  border-radius: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--color-border-subtle);
  background: rgba(255, 255, 255, 0.4);
  .tab-item {
    background: transparent;
    border-radius: 8px;
    border: none;
    font-size: 18px;
    font-weight: 400;
    line-height: normal;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &.disabled {
      cursor: not-allowed;
      pointer-events: auto;
      color: var(--black);

      svg {
        stroke: var(--black);
      }

      &:hover {
        background: transparent;
        transform: none;
      }
    }

    i {
      font-size: 16px;
    }

    &:hover {
      background-color: var(--white);
    }

    &.active {
      background: var(--white);
      color: var(--black);
      ava-icon {
        @include mixins.gradient-text(none, initial, initial);
      }

      i {
        @include mixins.gradient-text(inherit, text, transparent);
      }
    }
  }
  .tabs-container {
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }

    .tab-item {
      flex: 1;
      min-width: fit-content;
    }
  }
}
