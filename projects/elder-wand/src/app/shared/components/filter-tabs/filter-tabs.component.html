<div class="p-2 m-4 mt-5 filter-tabs" id="filter-tab-container">
  <div class="tabs-wrapper">
    <!-- Visible Tabs Container -->
    <div class="d-flex g-2 tabs-container">
      <button
        *ngFor="let tab of visibleTabs"
        class="d-flex align-items-center justify-content-center g-2 p-2 tab-item"
        [class.active]="isActiveTab(tab.id)"
        [class.disabled]="tab.disabled"
        [disabled]="tab.disabled"
        (click)="onTabClick(tab.id)"
      >
        <ava-icon
          *ngIf="tab.icon"
          [iconName]="tab.icon"
          iconSize="24px"
          [iconColor]="tab.iconColor || '#000'"
        ></ava-icon>
        <span>{{ tab.label }}</span>
      </button>
    </div>
  </div>
</div>
