import {
  Component,
  Input,
  Output,
  EventEmitter,
  HostListener,
  OnInit,
  AfterViewInit,
  OnChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '@ava/play-comp-library';
import { FilterTab } from '../../interfaces/launchpad.interface';

@Component({
  selector: 'app-filter-tabs',
  templateUrl: './filter-tabs.component.html',
  styleUrls: ['./filter-tabs.component.scss'],
  standalone: true,
  imports: [CommonModule, IconComponent],
})
export class FilterTabsComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() tabs: FilterTab[] = [];
  @Input() activeTab: string = 'all';
  @Output() tabChange = new EventEmitter<string>();
  sortedTabs: FilterTab[] = [];
  visibleTabs: FilterTab[] = [];
  dropdownTabs: FilterTab[] = [];
  showDropdown = false;

  ngOnInit(): void {
    this.updateSortedTabs();
    this.updateTabsVisibility();
  }

  ngOnChanges(): void {
    this.updateSortedTabs();
    this.updateTabsVisibility();
  }

  private updateSortedTabs(): void {
    this.sortedTabs = this.tabs
      .map((tab, index) => ({
        ...tab,
        priority: tab.priority ?? this.tabs.length - index,
      }))
      .sort((a, b) => (b.priority || 0) - (a.priority || 0));
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.updateTabsVisibility();
    });
  }

  @HostListener('window:resize')
  onResize() {
    this.updateTabsVisibility();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const filterButton = document.querySelector('.filter-dropdown-btn');
    const dropdown = document.querySelector('.filter-dropdown');
    if (
      !filterButton?.contains(event.target as Node) &&
      !dropdown?.contains(event.target as Node)
    ) {
      this.showDropdown = false;
    }
  }

  updateTabsVisibility() {
    const container = document.querySelector('.tabs-container');
    if (!container) return;
    const containerWidth = container.clientWidth;
    const filterButtonWidth = 100;
    const availableWidth = containerWidth - filterButtonWidth;
    this.visibleTabs = [];
    this.dropdownTabs = [];
    let currentWidth = 0;
    const averageTabWidth = availableWidth / this.sortedTabs.length;
    for (const tab of this.sortedTabs) {
      const estimatedWidth = this.calculateTabWidth(tab);
      if (
        currentWidth + estimatedWidth <= availableWidth &&
        estimatedWidth <= averageTabWidth * 1.5
      ) {
        this.visibleTabs.push(tab);
        currentWidth += estimatedWidth;
      } else {
        this.dropdownTabs.push(tab);
      }
    }
  }

  private calculateTabWidth(tab: FilterTab): number {
    const textWidth = tab.label.length * 8;
    const padding = 32;
    const iconWidth = tab.icon ? 24 : 0;
    const gap = 8;
    return textWidth + padding + iconWidth + gap;
  }

  toggleDropdown(event: Event) {
    event.stopPropagation();
    this.showDropdown = !this.showDropdown;
  }

  onTabClick(tabId: string): void {
    const tab = this.sortedTabs.find((t) => t.id === tabId);
    if (tab?.disabled) {
      return;
    }
    if (this.activeTab !== tabId) {
      this.activeTab = tabId;
      this.tabChange.emit(tabId);
    }
    this.showDropdown = false;
  }

  isActiveTab(tabId: string): boolean {
    return this.activeTab === tabId;
  }
}
