<div class="studios-container">
  <!-- Skeleton Loader -->
  <div *ngIf="showSkeleton" class="studios-grid">
    <div class="studio-card skeleton-card">
      <div class="card-content">
        <div class="card-header">
          <div class="skeleton-title"></div>
          <div class="skeleton-description"></div>
        </div>
        <div class="card-body">
          <div class="skeleton-image"></div>
          <div class="skeleton-arrow"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Actual Content -->
  <div *ngIf="!showSkeleton" class="studios-grid">
    <div
      class="studio-card"
      *ngFor="let studio of displayedStudios"
      (click)="navigateToStudio(studio)"
      role="button"
      tabindex="0"
      (keydown.enter)="navigateToStudio(studio)"
      (keydown.space)="navigateToStudio(studio)"
      [attr.aria-label]="'Navigate to ' + studio.title"
    >
      <div class="card-content">
        <div class="card-header">
          <h2>{{ studio.title }}</h2>
          <p class="description">{{ studio.description }}</p>
        </div>

        <div class="card-body">
          <div class="card-visual">
            <img [src]="studio.image" [alt]="studio.title" />
          </div>

          <div class="card-footer">
            <div class="arrow-button">
              <ava-icon
                iconName="move-right"
                iconSize="20px"
                iconColor="#A0A0A0"
              ></ava-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
