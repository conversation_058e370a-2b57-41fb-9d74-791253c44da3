export interface Studio {
  name: string;
  type: string;
  backgroundColor: string;
  textColor: string;
}

export interface Agent {
  id: string;
  title: string;
  description: string;
  rating?: number;
  users?: number;
  studio?: Studio;
  status?: 'approved' | 'pending' | 'denied' | 'draft';
  createdDate?: string;
  author?: string;
  timeAgo?: string;
}

// New interface to match the API response
export interface EntityResult {
  id: number;
  entityType: string;
  name: string;
  description: string;
  details: string;
  createdBy: string;
  modifiedBy: string;
  score: number;
  highlights: any;
  deleted: boolean;
}

// Extended Agent interface that includes API data
export interface AgentWithEntityData extends Agent {
  entityData?: EntityResult;
}
