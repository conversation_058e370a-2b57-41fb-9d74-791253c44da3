import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { StudiosComponent } from '../../shared/components/studios/studios.component';
import { AnalyticsCardComponent } from '../../shared/components/analytics-card/analytics-card.component';
import { Agent } from '../../shared/interfaces/agent-list.interface';
import { EntityService } from '../../shared/services/entity.service';
import {
  RevelioSearchResponse,
  RevelioSearchResult,
  SearchService,
} from '../../shared/services/search.service';
import { GlobalStoreService } from '../../shared/service/global-store.service';
import { Subscription } from 'rxjs';
import { CommonModule } from '@angular/common';
import { IconComponent } from '@ava/play-comp-library';
import {
  AgentCardAction,
  AgentCardComponent,
} from '../../shared/components/agent-card/agent-card.component';
import { DrawerService } from '../../shared/services/drawer.service';
import { Router } from '@angular/router';
import { Hero } from '../../shared/components';

@Component({
  selector: 'app-launchpad-home',
  standalone: true,
  imports: [
    AgentCardComponent,
    StudiosComponent,
    Hero,
    AnalyticsCardComponent,
    CommonModule,
    IconComponent,
  ],
  templateUrl: './launchpad-home.component.html',
  styleUrl: './launchpad-home.component.scss',
})
export class LaunchpadHomeComponent implements OnInit, OnDestroy {
  placeholderText: string[] = [
    'Search for the agents here',
    'Search for the agents here',
  ];
  agentsData: Agent[] = [];
  searchResults: RevelioSearchResult[] = [];
  searchQuery: string = '';
  isSearchLoading: boolean = false;
  selectedStudioIndex: number = 0;
  showAnalytics: boolean = false; // New property to control analytics visibility
  isLoading: boolean = false;
  isMarketplace: boolean = true; // Set to false for dashboard view
  showTwoColumns: boolean = true;

  private studioSubscription: Subscription = new Subscription();

  constructor(
    private entityService: EntityService,
    private globalStoreService: GlobalStoreService,
    private readonly drawerService: DrawerService,
    private readonly router: Router,
    private readonly searchService: SearchService,
  ) {}

  ngOnInit() {
    this.loadAgents();
  }

  ngOnDestroy() {
    this.studioSubscription.unsubscribe();
  }

  /**
   * Toggle analytics panel visibility
   */
  toggleAnalytics(): void {
    this.showAnalytics = !this.showAnalytics;
  }

  /**
   * Load agents and convert entity data to agent format
   */
  private loadAgents(): void {
    this.isLoading = true;
    this.entityService.getAgents(0, 50).subscribe({
      next: (entityAgents) => {
        // Convert entity agents to the expected agent format
        this.agentsData = entityAgents.map((entityAgent) => ({
          id: String(entityAgent.id),
          title: entityAgent.name,
          description: entityAgent.description,
          rating: 4.5, // Default rating since it's not in the API
          studio: {
            name: 'Experience Studio', // Default studio
            type: 'Experience Studio',
            backgroundColor: '#FFF4F9',
            textColor: '#DC047B',
          },
          users: Math.floor(Math.random() * 100) + 10, // Random users count for now
        }));
      },
      error: (error) => {
        console.error('Error loading agents:', error);
        // Fallback to empty array
        this.agentsData = [];
      },
      complete: () => {
        this.isLoading = false;
      },
    });
  }

  /**
   * Handle search query change from the search bar
   */
  onSearchQueryChange(query: string): void {
    this.isLoading = true;
    this.searchService
      .revelioSearch({
        query: query,
        limit: 10,
        threshold: 0,
      })
      .subscribe({
        next: (response: RevelioSearchResponse) => {
          // Extract results from the Revelio response
          const results: RevelioSearchResult[] = response.results || [];
          this.agentsData = results.map((entityAgent) => ({
            id: String(entityAgent.metadata.id),
            title: entityAgent.metadata.name,
            description: entityAgent.metadata.description,
            rating: 4.5, // Default rating since it's not in the API
            studio: {
              name: 'Experience Studio', // Default studio
              type: 'Experience Studio',
              backgroundColor: '#FFF4F9',
              textColor: '#DC047B',
            },
            users: Math.floor(Math.random() * 100) + 10, // Random users count for now
          }));
        },
        error: (error: any) => {
          console.error('Revelio search error:', error);
          this.searchResults = [];
        },
        complete: () => {
          this.isLoading = false;
        },
      });
  }

  getSkeletonCards(): number[] {
    return Array(8)
      .fill(0)
      .map((_, i) => i);
  }

  /**
   * Handle search loading change from the search bar
   */
  onSearchLoadingChange(isLoading: boolean): void {
    this.isSearchLoading = isLoading;
  }

  onAgentClick(agent: Agent): void {
    const entityAgent = this.agentsData.find((ea: any) => ea.id === agent.id);

    console.log('Showing agent details for:', agent, entityAgent);

    this.drawerService.open({
      data: { ...agent, entityData: entityAgent },
      width: '650px',
      position: 'right',
      onGoToPlayground: (selectedAgent: Agent) =>
        this.goToPlayground(selectedAgent),
    });
  }

  goToPlayground(agent: Agent): void {
    if (agent) {
      // You can replace this with the actual navigation logic
      this.router.navigate(['/build/agents/individual/execute'], {
        queryParams: {
          id: agent.id,
        },
      });
      // Close the drawer after navigation
      this.drawerService.close();
    }
  }

  onAgentAction(action: AgentCardAction): void {
    console.log('Agent action:', action);

    switch (action.type) {
      case 'view':
        break;
      case 'edit':
        break;
      case 'delete':
        break;
      case 'play':
        break;
    }
  }

  /**
   * Handle send clicked from the search bar
   */
  onSendClicked(query: string): void {}
}
