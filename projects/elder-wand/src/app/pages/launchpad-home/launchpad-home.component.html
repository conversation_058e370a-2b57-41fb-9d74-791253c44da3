<div id="launchpad-home-container" class="d-flex g-3">
  <div
    class="main-content-section"
    [class.full-width]="!showAnalytics"
    [class.with-analytics]="showAnalytics"
  >
    <div
      *ngIf="!showAnalytics"
      class="d-flex align-items-center justify-content-center analytics-toggle-btn"
      (click)="toggleAnalytics()"
    >
      <ava-icon
        iconName="chart-column-increasing"
        iconSize="24"
        iconColor="#F06896"
      ></ava-icon>
    </div>

    <div class="mt-5 hero-component-container">
      <app-hero
        [placeholderText]="placeholderText"
        (searchQueryChange)="onSearchQueryChange($event)"
        (sendClicked)="onSendClicked($event)"
      >
      </app-hero>
    </div>

    <div class="d-flex flex-column align-items-center px-3 content-section">
      <p>
        Here's a personalized overview of your day, organized to meet your
        current goals and responsibilities.
      </p>
      <div class="d-flex g-2 mt-4 two-column-layout">
        <div class="left-column">
          <app-studios
            [selectedStudioIndex]="selectedStudioIndex"
          ></app-studios>
        </div>
        <div class="pe-3 right-column">
          <div class="g-3 row agentsData-grid">
            <div
              [ngClass]="
                showTwoColumns ? 'col-12 col-md-6' : 'col-12 col-md-6 col-lg-3'
              "
              *ngFor="let skeleton of getSkeletonCards() | slice: 0 : 4"
              [hidden]="!isLoading"
            >
              <app-agent-card
                [isLoading]="true"
                [variant]="isMarketplace ? 'marketplace' : 'dashboard'"
              ></app-agent-card>
            </div>

            <!-- Actual agent cards -->
            <div
              [ngClass]="
                showTwoColumns ? 'col-12 col-md-6' : 'col-12 col-md-6 col-lg-3'
              "
              *ngFor="let agent of agentsData | slice: 0 : 4"
              [hidden]="isLoading"
            >
              <app-agent-card
                [agent]="agent"
                [isLoading]="false"
                [variant]="isMarketplace ? 'marketplace' : 'dashboard'"
                (cardClick)="onAgentClick($event)"
                (actionClick)="onAgentAction($event)"
              ></app-agent-card>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="d-flex flex-column justify-content-start align-items-center p-3 analytics-section"
    [class.show]="showAnalytics"
    [class.hide]="!showAnalytics"
  >
    <div class="analytics-header">
      <div
        class="d-flex justify-content-between align-items-center mb-3 analytics-title-row"
      >
        <h2 class="m-0">Analytics</h2>
        <button
          class="d-flex align-items-center justify-content-center p-1 close-btn"
          (click)="toggleAnalytics()"
        >
          <ava-icon iconName="x" iconSize="20" iconColor="#000"></ava-icon>
        </button>
      </div>
      <app-analytics-card></app-analytics-card>
    </div>
  </div>
</div>
