/**
 * =========================================================================
 * Play+ Design System: Elder Wand Theme
 *
 * A comprehensive theme system using Red, Purple, and Violet color families.
 * Implements semantic tokens, component-specific variables, and dark mode support.
 * =========================================================================
 */

/* =============================================================================
   BASE THEME CONFIGURATION
   ============================================================================= */

:root {
  /* Core System Variables */
  --ew-font-family:
    "Mulish", "Segoe UI", Roboto, Oxygen, "Helvetica Neue", sans-serif;
  --ew-background-image: url("../../../public/svgs/background.svg");

  /* Glass Effect Variables */
  --ew-glass-backdrop-blur: 12px;
  --ew-glass-border-width: 1px;
  --ew-glass-elevation: var(
    --global-elevation-02,
    0 4px 12px rgba(0, 0, 0, 0.12)
  );
}

/* =============================================================================
   LIGHT THEME
   ============================================================================= */

[data-theme="light"] {
  /* --- Color Palette --- */

  /* Red Color Family (Primary) */
  --ew-red-50: #fde9ef;
  --ew-red-100: #f8b9cf;
  --ew-red-200: #f598b7;
  --ew-red-300: #f06896;
  --ew-red-400: #ed4b82;
  --ew-red-500: #e91e63;
  --ew-red-600: #d41b5a;
  --ew-red-700: #a51546;
  --ew-red-800: #801136;
  --ew-red-900: #620d2a;

  /* Purple Color Family (Secondary) */
  --ew-purple-50: #f5e9f7;
  --ew-purple-100: #e0bce7;
  --ew-purple-200: #d19cdb;
  --ew-purple-300: #bd6eca;
  --ew-purple-400: #b052c0;
  --ew-purple-500: #9c27b0;
  --ew-purple-600: #8e23a0;
  --ew-purple-700: #6f1c7d;
  --ew-purple-800: #561561;
  --ew-purple-900: #42104a;

  /* Violet Color Family (Accent) */
  --ew-violet-50: #f0ebf8;
  --ew-violet-100: #d0c2e9;
  --ew-violet-200: #b9a4de;
  --ew-violet-300: #997bcf;
  --ew-violet-400: #8561c5;
  --ew-violet-500: #673ab7;
  --ew-violet-600: #5e35a7;
  --ew-violet-700: #492982;
  --ew-violet-800: #392065;
  --ew-violet-900: #2b184d;
  --black: #000000;
  --white: #ffffff;
  --text-caption: #666d99;
  --star-color: #FFD700;

  /* --- System Variables --- */
  --background-image: var(--ew-background-image);
  --font-family: var(--ew-font-family);

  /* --- Semantic Color Tokens --- */

  /* Brand Colors */
  --color-brand-primary: var(--ew-red-500);
  --color-brand-primary-hover: var(--ew-red-600);
  --color-brand-primary-active: var(--ew-red-700);
  --color-brand-secondary: var(--ew-purple-500);
  --color-brand-secondary-hover: var(--ew-purple-600);
  --color-brand-secondary-active: var(--ew-purple-700);
  --color-accent: var(--ew-violet-500);
  --color-accent-hover: var(--ew-violet-600);
  --color-accent-active: var(--ew-violet-700);

  /* Text Colors */
  --color-text-primary: #374151;
  --color-text-secondary: #616874;
  --color-text-placeholder: #9ca3af;
  --color-text-disabled: #9ca3af;
  --color-text-on-brand: #ffffff;
  --color-text-interactive: var(--ew-red-500);
  --color-text-interactive-hover: var(--ew-red-600);
  --color-text-success: #059669;
  --color-text-warning: #d97706;
  --color-text-error: var(--ew-red-500);
  --color-text-info: #2563eb;

  /* Background Colors */
  --color-background-primary: #ffffff;
  --color-background-secondary: var(--ew-red-50);
  --color-background-disabled: #f3f4f6;
  --color-background-success: #d1fae5;
  --color-background-warning: #fef3c7;
  --color-background-error: var(--ew-red-500);
  --color-background-info: #dbeafe;

  /* Surface Colors */
  --color-surface-interactive-primary: var(--ew-red-500);
  --color-surface-interactive-primary-hover: var(--ew-red-600);
  --color-surface-interactive-primary-active: var(--ew-red-700);
  --color-surface-interactive-secondary: var(--ew-purple-500);
  --color-surface-interactive-secondary-hover: var(--ew-purple-600);
  --color-surface-interactive-secondary-active: var(--ew-purple-700);
  --color-surface-interactive-accent: var(--ew-violet-500);
  --color-surface-interactive-accent-hover: var(--ew-violet-600);
  --color-surface-interactive-accent-active: var(--ew-violet-700);
  --color-surface-disabled: #e5e7eb;
  --color-surface-subtle-hover: #f9fafb;

  /* Border Colors */
  --color-border-default: #d1d5db;
  --color-border-subtle: #e5e7eb;
  --color-border-primary: var(--ew-red-500);
  --color-border-primary-hover: var(--ew-red-600);
  --color-border-primary-active: var(--ew-red-700);
  --color-border-secondary: var(--ew-purple-500);
  --color-border-secondary-hover: var(--ew-purple-600);
  --color-border-secondary-active: var(--ew-purple-700);
  --color-border-interactive: var(--ew-red-500);
  --color-border-focus: var(--ew-red-500);
  --color-border-success: #10b981;
  --color-border-warning: #f59e0b;
  --color-border-error: var(--ew-red-500);
  --color-border-info: #3b82f6;

  /* --- Glass Morphism System --- */
  --glass-backdrop-blur: var(--ew-glass-backdrop-blur);
  --glass-background-color: rgba(255, 255, 255, 0.25);
  --glass-border-color: rgba(255, 255, 255, 0.3);
  --glass-border-width: var(--ew-glass-border-width);
  --glass-elevation: var(--ew-glass-elevation);

  /* --- RGB Color Values (for rgba usage) --- */
  --rgb-brand-primary: 233, 30, 99;
  --rgb-brand-secondary: 156, 39, 176;
  --rgb-brand-tertiary: 103, 58, 183;
  --rgb-violet: 124, 58, 237;
  --rgb-white: 255, 255, 255;
  --rgb-black: 0, 0, 0;
  --rgb-neutral-100: 243, 244, 246;

  /* --- Effect Color System --- */
  --effect-color-primary: var(--rgb-brand-primary);
  --effect-color-secondary: var(--rgb-brand-secondary);
  --effect-color-accent: var(--rgb-violet);
  --effect-color-neutral: var(--rgb-black);
  --effect-color-surface: var(--rgb-white);

  /* --- Theme-Aware Component Tokens --- */

  /* Glass Metaphor */
  --surface-glass-bg: rgba(var(--effect-color-surface), 0.15);
  --surface-glass-border: rgba(var(--effect-color-surface), 0.7);
  --surface-glass-shadow: rgba(var(--effect-color-surface), 0.18);

  /* Light Metaphor */
  --color-light-glow: rgba(var(--effect-color-primary), 0.45);
  --color-light-glow-focus: rgba(var(--effect-color-primary), 0.65);
  --color-light-shadow: rgba(var(--effect-color-accent), 0.1);
  --color-light-border: var(--color-brand-primary);
  --color-light-border-focus: var(--color-brand-secondary);

  /* Liquid Metaphor */
  --color-liquid-shimmer-start: var(--color-brand-secondary);
  --color-liquid-shimmer-end: var(--color-brand-primary);

  /* Glass Variants */
  --glass-surface-color: var(--rgb-white);
  --glass-variant-primary: var(--rgb-brand-primary);
  --glass-variant-secondary: var(--rgb-brand-secondary);
  --glass-variant-success: 76, 175, 80;
  --glass-variant-warning: 255, 152, 0;
  --glass-variant-danger: 244, 67, 54;
  --glass-variant-info: 33, 150, 243;
  --glass-variant-purple: 156, 39, 176;
  --glass-variant-emerald: 16, 185, 129;

  /* --- Gradient System --- */
  --gradient-primary: linear-gradient(
    90deg,
    var(--ew-red-500) 0%,
    var(--ew-purple-500) 100%
  );
  --gradient-primary-hover: linear-gradient(
    90deg,
    var(--ew-red-600) 0%,
    var(--ew-purple-600) 100%
  );
  --gradient-secondary: linear-gradient(
    90deg,
    var(--ew-red-100) 0%,
    var(--ew-purple-100) 100%
  );
  --gradient-secondary-hover: linear-gradient(
    90deg,
    var(--ew-red-200) 0%,
    var(--ew-purple-200) 100%
  );
  --gradient-background: linear-gradient(
    135deg,
    var(--ew-red-50) 0%,
    var(--ew-purple-50) 100%
  );
  --gradient-card: linear-gradient(
    118deg,
    var(--ew-red-300),
    var(--ew-violet-300) 89.27%
  );

  /* ==========================================================================
     COMPONENT-SPECIFIC TOKENS
     ========================================================================== */

  /* --- Button System --- */
  --btn-primary-bg: var(--gradient-primary);
  --btn-primary-bg-hover: var(--gradient-primary-hover);
  --btn-primary-text: #ffffff;
  --btn-primary-shadow: rgba(var(--rgb-brand-primary), 0.3);
  --btn-primary-shadow-hover: rgba(var(--rgb-brand-secondary), 0.4);

  --btn-secondary-bg: var(--gradient-secondary);
  --btn-secondary-bg-hover: var(--gradient-secondary-hover);
  --btn-secondary-text: var(--ew-red-500);
  --btn-secondary-shadow: rgba(var(--rgb-brand-primary), 0.2);

  /* --- Console Card System --- */
  --console-card-bg: var(--gradient-background);
  --console-card-bg-hover: #f8fafc;
  --console-card-bg-disabled: rgba(255, 255, 255, 0.95);
  --console-card-primary-text: var(--ew-red-500);
  --console-card-primary-title: var(--ew-red-500);
  --console-card-primary-icon: var(--ew-red-500);
  --console-card-primary-border: var(--ew-red-500);
  --console-card-shadow: rgba(135, 161, 151, 0.12);
  --console-card-shadow-hover: rgba(0, 0, 0, 0.2);
  --console-card-shadow-focus: rgba(47, 90, 142, 0.15);
  --console-card-shadow-active: rgba(47, 90, 142, 0.2);
  --console-card-button-color: #6b7280;
  --console-card-button-hover-color: var(--ew-red-500);
  --console-card-button-focus-outline: var(--ew-red-500);
  --console-card-tooltip-bg: #1f2937;
  --console-card-tooltip-shadow: rgba(0, 0, 0, 0.3);
  --console-card-loading-border: #e0e0e0;
  --console-card-loading-spinner: var(--ew-red-400);
  --console-card-skeleton-start: #f0f0f0;
  --console-card-skeleton-middle: #e0e0e0;
  --console-card-skeleton-end: #f0f0f0;
  --console-card-skeleton-button-start: rgba(var(--rgb-brand-primary), 0.25);
  --console-card-skeleton-button-middle: rgba(var(--rgb-brand-primary), 0.25);
  --console-card-skeleton-button-end: rgba(var(--rgb-brand-primary), 0.25);

  /* --- Page Footer System --- */
  --page-footer-bg: linear-gradient(
    102.14deg,
    rgba(31, 41, 55, 0.8) 1.07%,
    rgba(31, 41, 55, 0.9) 98.01%
  );
  --page-footer-bg-hover: linear-gradient(
    102.14deg,
    rgba(31, 41, 55, 0.9) 1.07%,
    rgba(31, 41, 55, 1) 98.01%
  );
  --page-footer-bg-disabled: rgba(31, 41, 55, 0.6);
  --page-footer-border: rgba(var(--rgb-brand-primary), 0.3);
  --page-footer-border-hover: rgba(var(--rgb-brand-primary), 0.5);
  --page-footer-shadow: rgba(0, 0, 0, 0.1);
  --page-footer-shadow-hover: rgba(0, 0, 0, 0.2);
  --page-footer-text: var(--ew-red-500);
  --page-footer-text-secondary: #9ca3af;

  /* --- Pagination System --- */
  --pagination-primary-text: var(--ew-red-500);
  --pagination-primary-text-hover: #1f2937;
  --pagination-primary-bg: var(--ew-red-50);
  --pagination-primary-bg-hover: transparent;
  --pagination-primary-border: transparent;
  --pagination-button-bg: transparent;
  --pagination-button-bg-hover: transparent;
  --pagination-button-text: #374151;
  --pagination-button-text-hover: #1f2937;
  --pagination-button-border: transparent;
  --pagination-button-border-hover: transparent;
  --pagination-active-bg: var(--ew-red-500);
  --pagination-active-text: white;
  --pagination-active-border: transparent;
  --pagination-disabled-opacity: 0.5;
  --pagination-disabled-text: #9ca3af;
  --pagination-disabled-bg: #f3f4f6;
  --pagination-ellipsis: #374151;
  --pagination-focus-outline: #3b82f6;
  --pagination-focus-ring: rgba(59, 130, 246, 0.2);
  --pagination-arrow-color: #374151;
  --pagination-arrow-hover-color: #1f2937;

  /* --- Build Agents System --- */
  --build-agents-nav-bg: #ecebfe;
  --build-agents-nav-border: white;
  --build-agents-back-btn-text: #616874;
  --build-agents-toggle-bg: #f3f4f6;
  --build-agents-toggle-btn-text: #3b3f46;
  --build-agents-toggle-btn-hover: #374151;
  --build-agents-toggle-active-bg: var(--gradient-primary);
  --build-agents-toggle-active-text: #ffffff;
  --build-agents-toggle-shadow: rgba(0, 0, 0, 0.3);
  --build-agents-floater-bg: #ffffff;
  --build-agents-floater-border: #e5e7eb;
  --build-agents-floater-shadow: rgba(0, 0, 0, 0.1);
  --build-agents-floater-header-bg: #ffffff;
  --build-agents-floater-header-hover: #f9fafb;
  --build-agents-floater-header-border: #f3f4f6;
  --build-agents-floater-title: #1f2937;
  --build-agents-field-label: #374151;
  --build-agents-panel-bg: #ffffff;
  --build-agents-panel-border: #ffffff;
  --build-agents-panel-shadow: rgba(0, 0, 0, 0.08);
  --build-agents-panel-header-bg: #ffffff;
  --build-agents-panel-header-hover: #f9fafb;
  --build-agents-panel-title: #4c515b;
  --build-agents-panel-border-light: #f3f4f6;
  --build-agents-tab-bg: #f3f8fc;
  --build-agents-tab-border: #d1d5db;
  --build-agents-tab-icon: #6b7280;
  --build-agents-tab-label: #6b7280;
  --build-agents-tab-active-bg: var(--ew-red-500);
  --build-agents-tab-active-border: var(--ew-red-500);
  --build-agents-tab-active-icon: #ffffff;
  --build-agents-tab-active-label: #111827;
  --build-agents-search-bg: #f3f8fc;
  --build-agents-search-border: #e5e7eb;
  --build-agents-search-focus-border: var(--ew-red-500);
  --build-agents-search-focus-shadow: rgba(var(--rgb-brand-primary), 0.1);
  --build-agents-search-text: #111827;
  --build-agents-search-placeholder: #9ca3af;
  --build-agents-tool-bg: #ffffff;
  --build-agents-tool-border: #f0f1f2;
  --build-agents-tool-shadow: rgba(0, 0, 0, 0.05);
  --build-agents-tool-shadow-alt: rgba(0, 0, 0, 0.08);
  --build-agents-tool-hover-border: var(--ew-red-100);
  --build-agents-tool-hover-shadow: rgba(0, 0, 0, 0.1);
  --build-agents-tool-icon-bg: var(--ew-red-50);
  --build-agents-tool-icon: #ffffff;
  --build-agents-tool-name: #3b3f46;
  --build-agents-tool-count: #4c515b;
  --build-agents-tool-description: #616874;
  --build-agents-create-gradient: var(--gradient-primary);
  --build-agents-create-hover-gradient: var(--gradient-primary-hover);
  --build-agents-create-shadow: rgba(var(--rgb-brand-primary), 0.3);
  --build-agents-canvas-bg: var(--color-background-secondary);
  --build-agents-canvas-edge: #9ca1aa;
  --build-agents-canvas-marker: #9ca1aa;
  --build-agents-canvas-arrow: #9ca1aa;
  --build-agents-canvas-node-shadow: rgba(0, 0, 0, 0.1);
  --build-agents-canvas-node-hover-shadow: rgba(0, 0, 0, 0.15);
  --build-agents-canvas-node-border: #e5e7eb;
  --build-agents-canvas-node-bg: linear-gradient(
    135deg,
    #ffffff 0%,
    #f8fafc 100%
  );
  --build-agents-canvas-node-hover-border: var(--ew-red-500);
  --build-agents-canvas-node-hover-bg: linear-gradient(
    135deg,
    #ffffff 0%,
    #f1f5f9 100%
  );
  --build-agents-canvas-node-selected-border: var(--ew-red-500);
  --build-agents-canvas-node-selected-bg: linear-gradient(
    135deg,
    rgba(var(--rgb-brand-primary), 0.1) 0%,
    rgba(var(--rgb-brand-primary), 0.05) 100%
  );
  --build-agents-canvas-node-selected-shadow: rgba(
    var(--rgb-brand-primary),
    0.1
  );
  --build-agents-dropdown-bg: #ffffff;
  --build-agents-dropdown-border: #e5e7eb;
  --build-agents-dropdown-shadow: rgba(0, 0, 0, 0.1);
  --build-agents-dropdown-hover-border: var(--ew-red-500);
  --build-agents-dropdown-hover-shadow: rgba(var(--rgb-brand-primary), 0.1);
  --build-agents-dropdown-focus-border: var(--ew-red-500);
  --build-agents-dropdown-focus-shadow: rgba(var(--rgb-brand-primary), 0.2);
  --build-agents-execute-border: #e5e7eb;
  --build-agents-execute-connection: var(--ew-red-500);
  --build-agents-execute-arrow: var(--ew-red-500);
  --build-agents-playground-bg: #f8f9fa;
  --build-agents-playground-border: #e9ecef;
  --build-agents-playground-title: #495057;
  --build-agents-clear-chat-bg: #dc3545;
  --build-agents-clear-chat-hover: #c82333;
  --build-agents-file-upload-bg: var(--ew-red-500);
  --build-agents-file-upload-hover: var(--ew-red-600);
  --build-agents-file-item-bg: #e9ecef;
  --build-agents-file-item-border: #ced4da;
  --build-agents-file-remove: #6c757d;
  --build-agents-file-remove-hover: #dc3545;
  --build-agents-modal-error: #f87171;
  --build-agents-pulsating-border: #4b5563;
  --build-agents-pulsating-shadow: rgba(0, 0, 0, 0.3);
  --build-agents-pulsating-active-border: #f74646;
  --build-agents-pulsating-active-shadow: rgba(247, 70, 70, 0.5);
  --build-agents-pulsating-active-ring: rgba(247, 70, 70, 0.3);
  --build-agents-tool-icon-stroke: var(--ew-red-700);

  /* --- Custom Tabs System --- */
  --custom-tabs-bg: transparent;
  --custom-tabs-border: #6b7280;
  --custom-tabs-text: #f9fafb;
  --custom-tabs-text-active: #f9fafb;
  --custom-tabs-active-bg: var(--ew-purple-50);
  --custom-tabs-active-border: var(--ew-purple-500);
  --custom-tabs-active-icon: var(--ew-purple-500);
  --custom-tabs-active-icon-filter: brightness(0) saturate(100%) invert(27%)
    sepia(51%) saturate(2878%) hue-rotate(330deg) brightness(97%) contrast(87%);
  --custom-tabs-disabled-opacity: 0.5;
  --custom-tabs-icon-box-bg: transparent;
  --custom-tabs-icon-box-border: var(--ew-purple-200);
  --custom-tabs-icon-box-border-radius: 6px;
  --custom-tabs-label-text: #4b5563;
  --custom-tabs-label-active-text: #4b5563;
  --custom-tabs-label-font-weight: 500;
  --custom-tabs-label-active-font-weight: 500;

  /* --- Legacy Compatibility --- */
  --execution-header-background: var(--ew-violet-50);
  --svg-icon-color: var(--ew-red-500);
  --global-color-aqua-500: var(--ew-purple-500);
  --agent-execution-user-input-text: var(--ew-purple-400);
  --console-blue-100: var(--ew-red-100);
  --card-background-gradient: var(--gradient-card);
  --card-box-shadow: 0 4px 12px #87a1971f;
  --agents-preview-required: #f87171;
  --list-bg-color-selected: #f8d3df;

  /* --- Button Legacy Aliases --- */
  --button-primary-gradient: var(--gradient-primary);
  --button-primary-gradient-hover: var(--gradient-primary-hover);
  --button-primary-text: var(--btn-primary-text);
  --button-primary-shadow: var(--btn-primary-shadow);
  --button-primary-shadow-hover: var(--btn-primary-shadow-hover);
  --button-secondary-gradient: var(--gradient-secondary);
  --button-secondary-gradient-hover: var(--gradient-secondary-hover);
  --button-secondary-text: var(--btn-secondary-text);
  --button-secondary-shadow: var(--btn-secondary-shadow);
  --button-create-gradient: var(--gradient-primary);
  --button-create-gradient-hover: var(--gradient-primary-hover);
  --button-create-text: var(--btn-primary-text);
  --button-create-shadow: var(--btn-primary-shadow);
}
